1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.gymprojectmobile"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- <PERSON><PERSON>a ve galeri er<PERSON><PERSON><PERSON><PERSON> -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:3:5-65
17-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:3:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:4:5-80
18-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:4:22-77
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:5:5-81
19-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:5:22-78
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:6:5-76
20-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:6:22-73
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:45:5-50:15
29        <intent>
29-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:46:9-49:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:47:13-72
30-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:47:21-70
31
32            <data android:mimeType="text/plain" />
32-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:48:13-50
32-->C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\android\app\src\main\AndroidManifest.xml:48:19-48
33        </intent>
34    </queries>
35
36    <permission
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
37        android:name="com.example.gymprojectmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.example.gymprojectmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
41
42    <application
43        android:name="android.app.Application"
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
45        android:debuggable="true"
46        android:extractNativeLibs="true"
47        android:icon="@mipmap/ic_launcher"
48        android:label="gymprojectmobile" >
49        <activity
50            android:name="com.example.gymprojectmobile.MainActivity"
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
52            android:exported="true"
53            android:hardwareAccelerated="true"
54            android:launchMode="singleTop"
55            android:taskAffinity=""
56            android:theme="@style/LaunchTheme"
57            android:windowSoftInputMode="adjustResize" >
58
59            <!--
60                 Specifies an Android theme to apply to this Activity as soon as
61                 the Android process has started. This theme is visible to the user
62                 while the Flutter UI initializes. After that, this theme continues
63                 to determine the Window background behind the Flutter UI.
64            -->
65            <meta-data
66                android:name="io.flutter.embedding.android.NormalTheme"
67                android:resource="@style/NormalTheme" />
68
69            <intent-filter>
70                <action android:name="android.intent.action.MAIN" />
71
72                <category android:name="android.intent.category.LAUNCHER" />
73            </intent-filter>
74        </activity>
75        <!--
76             Don't delete the meta-data below.
77             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
78        -->
79        <meta-data
80            android:name="flutterEmbedding"
81            android:value="2" />
82
83        <provider
83-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
84            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
84-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
85            android:authorities="com.example.gymprojectmobile.flutter.image_provider"
85-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
86            android:exported="false"
86-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
87            android:grantUriPermissions="true" >
87-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
88            <meta-data
88-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
89                android:name="android.support.FILE_PROVIDER_PATHS"
89-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
90                android:resource="@xml/flutter_image_picker_file_paths" />
90-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
91        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
92        <service
92-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
93            android:name="com.google.android.gms.metadata.ModuleDependencies"
93-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
94            android:enabled="false"
94-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
95            android:exported="false" >
95-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
96            <intent-filter>
96-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
97                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
97-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
97-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
98            </intent-filter>
99
100            <meta-data
100-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
101                android:name="photopicker_activity:0:required"
101-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
102                android:value="" />
102-->[:image_picker_android] C:\Users\<USER>\Desktop\GymProject\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
103        </service>
104
105        <uses-library
105-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
106            android:name="androidx.window.extensions"
106-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
107            android:required="false" />
107-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
108        <uses-library
108-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
109            android:name="androidx.window.sidecar"
109-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
110            android:required="false" />
110-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
111
112        <provider
112-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
113            android:name="androidx.startup.InitializationProvider"
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
114            android:authorities="com.example.gymprojectmobile.androidx-startup"
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
115            android:exported="false" >
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
116            <meta-data
116-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
117-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
118                android:value="androidx.startup" />
118-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
119            <meta-data
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
120                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
121                android:value="androidx.startup" />
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
122        </provider>
123
124        <receiver
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
125            android:name="androidx.profileinstaller.ProfileInstallReceiver"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
126            android:directBootAware="false"
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
127            android:enabled="true"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
128            android:exported="true"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
129            android:permission="android.permission.DUMP" >
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
131                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
134                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
137                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
138            </intent-filter>
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
140                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
141            </intent-filter>
142        </receiver>
143    </application>
144
145</manifest>
