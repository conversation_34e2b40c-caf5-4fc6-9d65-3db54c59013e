/// Program Reset Dropdown Widget - GymKod Pro Mobile
///
/// Bu widget antrenman programını sıfırlama seçeneği sunar.
/// En az 1 gün tamamlanmışsa görünür, yoksa gizli kalır.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/workout_progress_provider.dart';

/// Program sıfırlama dropdown widget'ı
class ProgramResetDropdown extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Program adı (onay dialog'unda gösterilecek)
  final String programName;
  
  /// Dropdown icon'u
  final IconData icon;
  
  /// Dropdown boyutu
  final double? iconSize;
  
  /// Özel renk
  final Color? iconColor;

  const ProgramResetDropdown({
    super.key,
    required this.programId,
    required this.programName,
    this.icon = Icons.more_vert,
    this.iconSize,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    // Program ilerleme verilerini al
    final progress = ref.watch(programProgressProvider(programId));
    
    // Loading durumunu kontrol et
    final isLoading = ref.watch(workoutProgressLoadingProvider(programId));
    
    // En az 1 gün tamamlanmış mı kontrol et
    final hasAnyCompletion = progress?.dayCompletions.values.any((completed) => completed) ?? false;
    
    // Hiç tamamlama yoksa dropdown'ı gösterme
    if (!hasAnyCompletion) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      icon: isLoading
        ? SizedBox(
            width: iconSize ?? 24,
            height: iconSize ?? 24,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                iconColor ?? theme.colorScheme.onSurface,
              ),
            ),
          )
        : Icon(
            icon,
            size: iconSize ?? 24,
            color: iconColor ?? theme.colorScheme.onSurface,
          ),
      enabled: !isLoading,
      onSelected: (value) => _handleMenuSelection(context, ref, value),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'reset',
          child: Row(
            children: [
              Icon(
                Icons.refresh,
                color: theme.colorScheme.error,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Programı Baştan Başlat',
                style: TextStyle(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'stats',
          child: Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'İstatistikleri Gör',
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
      tooltip: 'Program Seçenekleri',
    );
  }

  /// Menü seçimini handle et
  Future<void> _handleMenuSelection(
    BuildContext context,
    WidgetRef ref,
    String value,
  ) async {
    switch (value) {
      case 'reset':
        await _showResetConfirmationDialog(context, ref);
        break;
      case 'stats':
        _showStatsDialog(context, ref);
        break;
    }
  }

  /// Program sıfırlama onay dialog'u
  Future<void> _showResetConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final theme = Theme.of(context);
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: theme.colorScheme.error,
              size: 28,
            ),
            const SizedBox(width: 12),
            const Text('Programı Sıfırla'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '"$programName" programının tüm ilerlemesi silinecek ve program baştan başlayacak.',
              style: theme.textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: theme.colorScheme.onErrorContainer,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Bu işlem geri alınamaz!',
                      style: TextStyle(
                        color: theme.colorScheme.onErrorContainer,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text('Emin misiniz?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
              foregroundColor: theme.colorScheme.onError,
            ),
            child: const Text('Sıfırla'),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      await _resetProgram(context, ref);
    }
  }

  /// Program sıfırlama işlemi
  Future<void> _resetProgram(BuildContext context, WidgetRef ref) async {
    try {
      await ref.read(workoutProgressProvider.notifier).resetProgramProgress(programId);

      // Başarı mesajını kontrol et ve göster
      final successMessage = ref.read(workoutProgressSuccessProvider);
      if (successMessage != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(successMessage),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        
        // Başarı mesajını temizle
        ref.read(workoutProgressProvider.notifier).clearSuccessMessage();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Program sıfırlama işlemi sırasında hata oluştu'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// İstatistik dialog'u
  void _showStatsDialog(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final progress = ref.read(programProgressProvider(programId));
    
    if (progress == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.analytics_outlined,
              color: theme.colorScheme.primary,
              size: 28,
            ),
            const SizedBox(width: 12),
            const Text('Program İstatistikleri'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow(
              context,
              'Mevcut Döngü',
              '${progress.currentCycle}. döngü',
              Icons.repeat,
            ),
            const SizedBox(height: 12),
            _buildStatRow(
              context,
              'Bu Döngüde Tamamlanan',
              '${progress.getCompletedDaysInCurrentCycle()} gün',
              Icons.check_circle_outline,
            ),
            const SizedBox(height: 12),
            _buildStatRow(
              context,
              'Toplam Tamamlama',
              '${progress.totalCompletions} gün',
              Icons.fitness_center,
            ),
            if (progress.lastActivityDate != null) ...[
              const SizedBox(height: 12),
              _buildStatRow(
                context,
                'Son Aktivite',
                _formatDate(progress.lastActivityDate!),
                Icons.schedule,
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Kapat'),
          ),
        ],
      ),
    );
  }

  /// İstatistik satırı widget'ı
  Widget _buildStatRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              Text(
                value,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Tarihi formatla
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Bugün';
    } else if (difference.inDays == 1) {
      return 'Dün';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} gün önce';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
