{"logs": [{"outputFile": "com.example.gymprojectmobile.app-mergeDebugResources-40:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fab08a6f6e27ebe9881e5845ef81bc7\\transformed\\core-1.13.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2836,2934,3036,3133,3237,3341,3446,4038", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2929,3031,3128,3232,3336,3441,3557,4134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\89bb887b47984212ea06b6247b94e176\\transformed\\appcompat-1.1.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,899,990,1082,1177,1271,1372,1465,1560,1665,1756,1847,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,82,90,91,94,93,100,92,94,104,90,90,84,104,105,102,106,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,811,894,985,1077,1172,1266,1367,1460,1555,1660,1751,1842,1927,2032,2138,2241,2348,2457,2564,2734,2831,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,899,990,1082,1177,1271,1372,1465,1560,1665,1756,1847,1932,2037,2143,2246,2353,2462,2569,2739,3952", "endColumns": "106,100,105,85,103,121,84,82,90,91,94,93,100,92,94,104,90,90,84,104,105,102,106,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,811,894,985,1077,1172,1266,1367,1460,1555,1660,1751,1842,1927,2032,2138,2241,2348,2457,2564,2734,2831,4033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00e4bc24da987e892a8683a085ee5f6a\\transformed\\preference-1.2.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,751", "endColumns": "70,86,79,151,168,86,82", "endOffsets": "171,258,338,490,659,746,829"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3562,3633,3720,3800,4139,4308,4395", "endColumns": "70,86,79,151,168,86,82", "endOffsets": "3628,3715,3795,3947,4303,4390,4473"}}]}]}