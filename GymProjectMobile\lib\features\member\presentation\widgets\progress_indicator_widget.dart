/// Progress Indicator Widget - GymKod Pro Mobile
///
/// Bu widget antrenman programı ilerleme durumunu gösterir.
/// Tamamlanan gün sayısı, yüzde ve görsel progress bar içerir.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/models.dart';
import '../providers/workout_progress_provider.dart';

/// İlerleme göstergesi widget'ı
class ProgressIndicatorWidget extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Toplam antrenman günü sayısı
  final int totalWorkoutDays;
  
  /// Program adı
  final String programName;
  
  /// Özel stil
  final ProgressIndicatorStyle? style;
  
  /// Animasyon süresi
  final Duration animationDuration;

  const ProgressIndicatorWidget({
    super.key,
    required this.programId,
    required this.totalWorkoutDays,
    required this.programName,
    this.style,
    this.animationDuration = const Duration(milliseconds: 500),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final progress = ref.watch(programProgressProvider(programId));
    
    if (progress == null) {
      return const SizedBox.shrink();
    }

    final effectiveStyle = style ?? ProgressIndicatorStyle.defaultStyle(theme);
    final completedDays = progress.getCompletedDaysInCurrentCycle();
    final progressPercentage = progress.getProgressPercentage(totalWorkoutDays);

    return Container(
      padding: effectiveStyle.padding,
      decoration: effectiveStyle.decoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Başlık
          Row(
            children: [
              Icon(
                effectiveStyle.titleIcon,
                color: effectiveStyle.titleIconColor,
                size: effectiveStyle.titleIconSize,
              ),
              SizedBox(width: effectiveStyle.spacing),
              Expanded(
                child: Text(
                  effectiveStyle.title,
                  style: effectiveStyle.titleStyle,
                ),
              ),
              if (progress.currentCycle > 1)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${progress.currentCycle}. Döngü',
                    style: TextStyle(
                      color: Colors.orange,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          
          SizedBox(height: effectiveStyle.spacing),
          
          // Progress Bar
          AnimatedContainer(
            duration: animationDuration,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '$completedDays/$totalWorkoutDays gün tamamlandı',
                      style: effectiveStyle.subtitleStyle,
                    ),
                    Text(
                      '${(progressPercentage * 100).toInt()}%',
                      style: effectiveStyle.percentageStyle,
                    ),
                  ],
                ),
                
                SizedBox(height: effectiveStyle.spacing / 2),
                
                ClipRRect(
                  borderRadius: BorderRadius.circular(effectiveStyle.progressBarHeight / 2),
                  child: LinearProgressIndicator(
                    value: progressPercentage,
                    backgroundColor: effectiveStyle.progressBackgroundColor,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      effectiveStyle.progressColor,
                    ),
                    minHeight: effectiveStyle.progressBarHeight,
                  ),
                ),
              ],
            ),
          ),
          
          // Ek bilgiler
          if (effectiveStyle.showExtraInfo) ...[
            SizedBox(height: effectiveStyle.spacing),
            _buildExtraInfo(context, progress, effectiveStyle),
          ],
        ],
      ),
    );
  }

  /// Ek bilgi widget'ı
  Widget _buildExtraInfo(
    BuildContext context,
    ProgramProgressModel progress,
    ProgressIndicatorStyle style,
  ) {
    return Row(
      children: [
        if (progress.totalCompletions > 0) ...[
          Icon(
            Icons.fitness_center,
            color: style.extraInfoIconColor,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            'Toplam: ${progress.totalCompletions} gün',
            style: style.extraInfoStyle,
          ),
        ],
        
        if (progress.lastActivityDate != null) ...[
          const SizedBox(width: 16),
          Icon(
            Icons.schedule,
            color: style.extraInfoIconColor,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            'Son: ${_formatDate(progress.lastActivityDate!)}',
            style: style.extraInfoStyle,
          ),
        ],
      ],
    );
  }

  /// Tarihi formatla
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Bugün';
    } else if (difference.inDays == 1) {
      return 'Dün';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} gün önce';
    } else {
      return '${date.day}/${date.month}';
    }
  }
}

/// İlerleme göstergesi stil sınıfı
class ProgressIndicatorStyle {
  /// Container padding'i
  final EdgeInsetsGeometry padding;
  
  /// Container dekorasyonu
  final BoxDecoration? decoration;
  
  /// Başlık
  final String title;
  
  /// Başlık stili
  final TextStyle titleStyle;
  
  /// Başlık icon'u
  final IconData titleIcon;
  
  /// Başlık icon rengi
  final Color titleIconColor;
  
  /// Başlık icon boyutu
  final double titleIconSize;
  
  /// Alt başlık stili
  final TextStyle subtitleStyle;
  
  /// Yüzde stili
  final TextStyle percentageStyle;
  
  /// Progress bar yüksekliği
  final double progressBarHeight;
  
  /// Progress bar rengi
  final Color progressColor;
  
  /// Progress bar arka plan rengi
  final Color progressBackgroundColor;
  
  /// Element arası boşluk
  final double spacing;
  
  /// Ek bilgi göster
  final bool showExtraInfo;
  
  /// Ek bilgi stili
  final TextStyle extraInfoStyle;
  
  /// Ek bilgi icon rengi
  final Color extraInfoIconColor;

  const ProgressIndicatorStyle({
    required this.padding,
    this.decoration,
    required this.title,
    required this.titleStyle,
    required this.titleIcon,
    required this.titleIconColor,
    required this.titleIconSize,
    required this.subtitleStyle,
    required this.percentageStyle,
    required this.progressBarHeight,
    required this.progressColor,
    required this.progressBackgroundColor,
    this.spacing = 8.0,
    this.showExtraInfo = true,
    required this.extraInfoStyle,
    required this.extraInfoIconColor,
  });

  /// Varsayılan stil
  factory ProgressIndicatorStyle.defaultStyle(ThemeData theme) {
    return ProgressIndicatorStyle(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      title: 'Bu Döngüdeki İlerleme',
      titleStyle: theme.textTheme.titleMedium!.copyWith(
        fontWeight: FontWeight.w600,
      ),
      titleIcon: Icons.trending_up,
      titleIconColor: theme.colorScheme.primary,
      titleIconSize: 20,
      subtitleStyle: theme.textTheme.bodyMedium!,
      percentageStyle: theme.textTheme.bodyMedium!.copyWith(
        fontWeight: FontWeight.bold,
        color: theme.colorScheme.primary,
      ),
      progressBarHeight: 8,
      progressColor: theme.colorScheme.primary,
      progressBackgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
      extraInfoStyle: theme.textTheme.bodySmall!.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      extraInfoIconColor: theme.colorScheme.onSurface.withValues(alpha: 0.7),
    );
  }

  /// Kompakt stil
  factory ProgressIndicatorStyle.compactStyle(ThemeData theme) {
    return ProgressIndicatorStyle(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      title: 'İlerleme',
      titleStyle: theme.textTheme.titleSmall!.copyWith(
        fontWeight: FontWeight.w600,
      ),
      titleIcon: Icons.trending_up,
      titleIconColor: theme.colorScheme.primary,
      titleIconSize: 18,
      subtitleStyle: theme.textTheme.bodySmall!,
      percentageStyle: theme.textTheme.bodySmall!.copyWith(
        fontWeight: FontWeight.bold,
        color: theme.colorScheme.primary,
      ),
      progressBarHeight: 6,
      progressColor: theme.colorScheme.primary,
      progressBackgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
      spacing: 6.0,
      showExtraInfo: false,
      extraInfoStyle: theme.textTheme.bodySmall!,
      extraInfoIconColor: theme.colorScheme.onSurface.withValues(alpha: 0.7),
    );
  }

  /// Minimal stil
  factory ProgressIndicatorStyle.minimalStyle(ThemeData theme) {
    return ProgressIndicatorStyle(
      padding: const EdgeInsets.symmetric(vertical: 8),
      title: '',
      titleStyle: const TextStyle(),
      titleIcon: Icons.trending_up,
      titleIconColor: theme.colorScheme.primary,
      titleIconSize: 0,
      subtitleStyle: theme.textTheme.bodySmall!,
      percentageStyle: theme.textTheme.bodySmall!.copyWith(
        fontWeight: FontWeight.bold,
        color: theme.colorScheme.primary,
      ),
      progressBarHeight: 4,
      progressColor: theme.colorScheme.primary,
      progressBackgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
      spacing: 4.0,
      showExtraInfo: false,
      extraInfoStyle: theme.textTheme.bodySmall!,
      extraInfoIconColor: theme.colorScheme.onSurface.withValues(alpha: 0.7),
    );
  }
}
