/// Complete Workout Button Widget - GymKod Pro Mobile
///
/// Bu widget antrenman seansını tamamlama butonu sağlar.
/// Tüm egzersizler tamamlandığında aktif olur.
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/exercise_progress_provider.dart';

/// Antrenman tamamlama butonu widget'ı
class CompleteWorkoutButton extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Gün ID'si
  final int dayId;
  
  /// Gün adı
  final String dayName;
  
  /// Tüm egzersizlerin ID'leri
  final List<int> allExerciseIds;
  
  /// Buton stili
  final CompleteWorkoutButtonStyle? style;

  const CompleteWorkoutButton({
    super.key,
    required this.programId,
    required this.dayId,
    required this.dayName,
    required this.allExerciseIds,
    this.style,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final effectiveStyle = style ?? CompleteWorkoutButtonStyle.defaultStyle(theme);
    
    // Antrenman seansının durumunu kontrol et
    final sessionKey = '${programId}_$dayId';
    final isSessionCompleted = ref.watch(workoutSessionCompletionProvider(sessionKey));
    final isSessionLoading = ref.watch(workoutSessionLoadingProvider(sessionKey));
    
    // Tüm egzersizlerin tamamlanıp tamamlanmadığını kontrol et
    final allExercisesCompleted = ref.watch(exerciseProgressProvider).isDayCompleted(
      programId, dayId, allExerciseIds
    );

    // Buton durumunu belirle
    final isEnabled = allExercisesCompleted && !isSessionCompleted && !isSessionLoading;

    if (isSessionCompleted) {
      return _buildCompletedState(effectiveStyle);
    }

    return SizedBox(
      width: double.infinity,
      height: effectiveStyle.height,
      child: ElevatedButton(
        onPressed: isEnabled ? () => _handleCompleteWorkout(context, ref) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: isEnabled 
            ? effectiveStyle.enabledBackgroundColor
            : effectiveStyle.disabledBackgroundColor,
          foregroundColor: isEnabled 
            ? effectiveStyle.enabledTextColor
            : effectiveStyle.disabledTextColor,
          elevation: isEnabled ? effectiveStyle.elevation : 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(effectiveStyle.borderRadius),
          ),
        ),
        child: isSessionLoading
          ? _buildLoadingContent(effectiveStyle)
          : _buildButtonContent(effectiveStyle, isEnabled),
      ),
    );
  }

  /// Loading içeriği
  Widget _buildLoadingContent(CompleteWorkoutButtonStyle style) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(style.enabledTextColor),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'Tamamlanıyor...',
          style: TextStyle(
            fontSize: style.fontSize,
            fontWeight: style.fontWeight,
          ),
        ),
      ],
    );
  }

  /// Buton içeriği
  Widget _buildButtonContent(CompleteWorkoutButtonStyle style, bool isEnabled) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          isEnabled ? Icons.check_circle : Icons.radio_button_unchecked,
          size: style.iconSize,
        ),
        const SizedBox(width: 12),
        Text(
          isEnabled ? 'Antrenmanı Bitir' : 'Önce Egzersizleri Tamamlayın',
          style: TextStyle(
            fontSize: style.fontSize,
            fontWeight: style.fontWeight,
          ),
        ),
      ],
    );
  }

  /// Tamamlanmış durum widget'ı
  Widget _buildCompletedState(CompleteWorkoutButtonStyle style) {
    return Container(
      width: double.infinity,
      height: style.height,
      decoration: BoxDecoration(
        color: style.completedBackgroundColor,
        borderRadius: BorderRadius.circular(style.borderRadius),
        border: Border.all(
          color: style.completedBorderColor,
          width: 2,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: style.completedTextColor,
            size: style.iconSize,
          ),
          const SizedBox(width: 12),
          Text(
            'Antrenmanınız Tamamlandı! 🎉',
            style: TextStyle(
              color: style.completedTextColor,
              fontSize: style.fontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Antrenman tamamlama işlemini handle et
  Future<void> _handleCompleteWorkout(BuildContext context, WidgetRef ref) async {
    try {
      // Haptic feedback
      HapticFeedback.mediumImpact();
      
      // Antrenman seansını tamamla
      await ref.read(exerciseProgressProvider.notifier).completeWorkoutSession(
        programId,
        dayId,
      );

      // Başarı mesajını kontrol et ve göster
      final successMessage = ref.read(exerciseProgressSuccessProvider);
      if (successMessage != null && context.mounted) {
        _showCompletionDialog(context, successMessage);
        
        // Başarı mesajını temizle
        ref.read(exerciseProgressProvider.notifier).clearSuccessMessage();
      }
    } catch (e) {
      // Hata durumunda kullanıcıya bilgi ver
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Antrenman tamamlama işlemi sırasında hata oluştu'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Antrenman tamamlama dialog'u
  void _showCompletionDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.celebration, color: Colors.orange, size: 30),
            const SizedBox(width: 10),
            const Text('Tebrikler! 🎉'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 15),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.fitness_center, color: Colors.green),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '$dayName antrenmanınız başarıyla tamamlandı!',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Harika! 💪'),
          ),
        ],
      ),
    );
  }
}

/// Complete Workout Button Style
class CompleteWorkoutButtonStyle {
  /// Buton yüksekliği
  final double height;
  
  /// Buton border radius'u
  final double borderRadius;
  
  /// Buton elevation'ı
  final double elevation;
  
  /// Font boyutu
  final double fontSize;
  
  /// Font ağırlığı
  final FontWeight fontWeight;
  
  /// Icon boyutu
  final double iconSize;
  
  /// Aktif durum arka plan rengi
  final Color enabledBackgroundColor;
  
  /// Aktif durum text rengi
  final Color enabledTextColor;
  
  /// Pasif durum arka plan rengi
  final Color disabledBackgroundColor;
  
  /// Pasif durum text rengi
  final Color disabledTextColor;
  
  /// Tamamlanmış durum arka plan rengi
  final Color completedBackgroundColor;
  
  /// Tamamlanmış durum text rengi
  final Color completedTextColor;
  
  /// Tamamlanmış durum border rengi
  final Color completedBorderColor;

  const CompleteWorkoutButtonStyle({
    required this.height,
    required this.borderRadius,
    required this.elevation,
    required this.fontSize,
    required this.fontWeight,
    required this.iconSize,
    required this.enabledBackgroundColor,
    required this.enabledTextColor,
    required this.disabledBackgroundColor,
    required this.disabledTextColor,
    required this.completedBackgroundColor,
    required this.completedTextColor,
    required this.completedBorderColor,
  });

  /// Varsayılan stil
  factory CompleteWorkoutButtonStyle.defaultStyle(ThemeData theme) {
    return CompleteWorkoutButtonStyle(
      height: 48,
      borderRadius: 10,
      elevation: 2,
      fontSize: 14,
      fontWeight: FontWeight.w600,
      iconSize: 20,
      enabledBackgroundColor: Colors.green,
      enabledTextColor: Colors.white,
      disabledBackgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
      disabledTextColor: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      completedBackgroundColor: Colors.green.withValues(alpha: 0.1),
      completedTextColor: Colors.green,
      completedBorderColor: Colors.green,
    );
  }

  /// Kompakt stil
  factory CompleteWorkoutButtonStyle.compactStyle(ThemeData theme) {
    return CompleteWorkoutButtonStyle(
      height: 48,
      borderRadius: 8,
      elevation: 1,
      fontSize: 14,
      fontWeight: FontWeight.w600,
      iconSize: 20,
      enabledBackgroundColor: Colors.green,
      enabledTextColor: Colors.white,
      disabledBackgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
      disabledTextColor: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      completedBackgroundColor: Colors.green.withValues(alpha: 0.1),
      completedTextColor: Colors.green,
      completedBorderColor: Colors.green,
    );
  }

  /// Büyük stil
  factory CompleteWorkoutButtonStyle.largeStyle(ThemeData theme) {
    return CompleteWorkoutButtonStyle(
      height: 64,
      borderRadius: 16,
      elevation: 4,
      fontSize: 18,
      fontWeight: FontWeight.bold,
      iconSize: 28,
      enabledBackgroundColor: Colors.green,
      enabledTextColor: Colors.white,
      disabledBackgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
      disabledTextColor: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      completedBackgroundColor: Colors.green.withValues(alpha: 0.1),
      completedTextColor: Colors.green,
      completedBorderColor: Colors.green,
    );
  }
}
