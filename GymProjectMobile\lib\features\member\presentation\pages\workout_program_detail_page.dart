/// Workout Program Detail Page - GymKod Pro Mobile
///
/// Bu sayfa antrenman programının detayını gösterir.
/// <PERSON><PERSON><PERSON><PERSON>, egzersizler, set/tekrar bilgileri
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../providers/workout_program_detail_provider.dart';
import '../providers/exercise_progress_provider.dart';
import '../widgets/exercise_completion_checkbox.dart';
import '../widgets/complete_workout_button.dart';
import '../widgets/program_reset_button.dart';

class WorkoutProgramDetailPage extends ConsumerStatefulWidget {
  final int memberWorkoutProgramId;
  final String programName;

  const WorkoutProgramDetailPage({
    super.key,
    required this.memberWorkoutProgramId,
    required this.programName,
  });

  @override
  ConsumerState<WorkoutProgramDetailPage> createState() => _WorkoutProgramDetailPageState();
}

class _WorkoutProgramDetailPageState extends ConsumerState<WorkoutProgramDetailPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // Sayfa açıldığında program detayını yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(workoutProgramDetailProvider.notifier).loadProgramDetail(widget.memberWorkoutProgramId);

      // Program detayı yüklendikten sonra egzersiz progress'lerini yükle ve akıllı scroll yap
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final programDetail = ref.read(workoutProgramDetailDataProvider);
        if (programDetail != null) {
          // Yapılması gereken güne hemen scroll yap
          final nextWorkoutDayId = _findNextWorkoutDay();
          if (nextWorkoutDayId != null) {
            // Kısa bir gecikme ile scroll yap (UI render edilsin diye)
            Future.delayed(const Duration(milliseconds: 100), () {
              _scrollToDay(nextWorkoutDayId, programDetail.days.toList());
            });
          }

          // Egzersiz progress'lerini arka planda yükle
          Future.delayed(const Duration(milliseconds: 200), () {
            for (final day in programDetail.days) {
              if (!day.isRestDay && day.exercises.isNotEmpty) {
                final exerciseIds = day.exercises.map((e) => e.workoutProgramExerciseID).toList();
                ref.read(exerciseProgressProvider.notifier).loadDayExercises(
                  widget.memberWorkoutProgramId,
                  day.workoutProgramDayID,
                  exerciseIds,
                );
              }
            }
          });
        }
      });

      LoggingService.info('Workout program detail page loaded', tag: 'WORKOUT_DETAIL');
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Yapılması gereken bir sonraki antrenman gününü bulur
  int? _findNextWorkoutDay() {
    final programDetail = ref.read(workoutProgramDetailDataProvider);
    if (programDetail == null) return null;

    final workoutDays = programDetail.days.where((day) => !day.isRestDay).toList();

    // Tüm antrenman günlerinin tamamlanma durumunu kontrol et
    for (final day in workoutDays) {
      final sessionKey = '${widget.memberWorkoutProgramId}_${day.workoutProgramDayID}';
      final isCompleted = ref.read(workoutSessionCompletionProvider(sessionKey));

      if (!isCompleted) {
        return day.workoutProgramDayID;
      }
    }

    // Eğer tüm günler tamamlandıysa null döndür
    return null;
  }

  /// Belirli bir güne scroll yapar
  void _scrollToDay(int dayId, List<WorkoutProgramDayModel> days) {
    final dayIndex = days.indexWhere((day) => day.workoutProgramDayID == dayId);
    if (dayIndex != -1) {
      // Her gün kartının yaklaşık yüksekliği + spacing
      final cardHeight = 400.0; // Ortalama kart yüksekliği
      final spacing = 16.0;
      final headerHeight = 200.0; // Program reset button + spacing

      final targetOffset = headerHeight + (dayIndex * (cardHeight + spacing));

      // Hemen scroll yap, bekleme yok
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOutCubic,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        final theme = Theme.of(context);

        return Scaffold(
          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: Stack(
                  children: [
                    // Main Content
                    Column(
                      children: [
                        // Responsive Content Section
                        Expanded(
                          child: Padding(
                            padding: AppSpacing.responsiveScreenPadding(context),
                            child: _buildResponsiveProgramDetailContent(theme, deviceType),
                          ),
                        ),
                      ],
                    ),

                    // Fixed Back Button
                    _buildFixedBackButton(theme, deviceType),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Responsive Program Detail Content
  Widget _buildResponsiveProgramDetailContent(ThemeData theme, DeviceType deviceType) {
    final isLoading = ref.watch(workoutProgramDetailLoadingProvider);
    final isRefreshing = ref.watch(workoutProgramDetailRefreshingProvider);
    final error = ref.watch(workoutProgramDetailErrorProvider);
    final programDetail = ref.watch(workoutProgramDetailDataProvider);

    if (isLoading && !isRefreshing) {
      return _buildLoadingState(theme, deviceType);
    }

    if (error != null) {
      return _buildErrorState(theme, deviceType, error);
    }

    if (programDetail == null) {
      return _buildNoProgramState(theme, deviceType);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(workoutProgramDetailProvider.notifier).refreshProgramDetail(widget.memberWorkoutProgramId);
      },
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Modern Program Header
            _buildProgramHeader(theme, deviceType, programDetail),

            ResponsiveSpacing.vertical(
              mobile: 20.0,
              tablet: 24.0,
              desktop: 28.0,
            ),

            // Program sıfırlama butonu (tüm antrenmanlar tamamlandıysa)
            Consumer(
              builder: (context, ref, child) {
                // Tüm antrenman günlerinin ID'lerini ve egzersiz ID'lerini topla
                final allWorkoutDayIds = programDetail.days
                    .where((day) => !day.isRestDay)
                    .map((day) => day.workoutProgramDayID)
                    .toList();

                final allExerciseIds = <int, List<int>>{};
                for (final day in programDetail.days) {
                  if (!day.isRestDay) {
                    allExerciseIds[day.workoutProgramDayID] =
                        day.exercises.map((e) => e.workoutProgramExerciseID).toList();
                  }
                }

                return ProgramResetButton(
                  programId: widget.memberWorkoutProgramId,
                  programName: widget.programName,
                  allWorkoutDayIds: allWorkoutDayIds,
                  allExerciseIds: allExerciseIds,
                );
              },
            ),

            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 12.0,
              desktop: 16.0,
            ),

            // Program günleri (dayNumber'a göre sıralı)
            ...(programDetail.days.toList()
                ..sort((a, b) => a.dayNumber.compareTo(b.dayNumber)))
                .map((day) => Padding(
                  padding: EdgeInsets.only(
                    bottom: AppSpacing.responsive(context,
                      mobile: 12.0,
                      tablet: 16.0,
                      desktop: 20.0,
                    ),
                  ),
                  child: _buildWorkoutDayCard(theme, deviceType, day),
                )),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
          ],
        ),
      ),
    );
  }

  /// Loading durumu
  Widget _buildLoadingState(ThemeData theme, DeviceType deviceType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ResponsiveText(
            'Program detayı yükleniyor...',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Hata durumu
  Widget _buildErrorState(ThemeData theme, DeviceType deviceType, String error) {
    return Center(
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              color: theme.colorScheme.error,
            ),
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            ResponsiveText(
              'Hata Oluştu',
              textType: 'h3',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 10.0,
              desktop: 12.0,
            ),
            ResponsiveText(
              error,
              textType: 'bodymedium',
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
            ElevatedButton(
              onPressed: () {
                ref.read(workoutProgramDetailProvider.notifier).loadProgramDetail(widget.memberWorkoutProgramId);
              },
              child: const ResponsiveText(
                'Tekrar Dene',
                textType: 'button',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Program yok durumu
  Widget _buildNoProgramState(ThemeData theme, DeviceType deviceType) {
    return Center(
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.fitness_center_outlined,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              color: theme.colorScheme.primary,
            ),
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            ResponsiveText(
              'Program Bulunamadı',
              textType: 'h3',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 10.0,
              desktop: 12.0,
            ),
            ResponsiveText(
              'Bu program detayına erişiminiz bulunmuyor veya program silinmiş olabilir.',
              textType: 'bodymedium',
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const ResponsiveText(
                'Geri Dön',
                textType: 'button',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Antrenman günü kartı
  Widget _buildWorkoutDayCard(ThemeData theme, DeviceType deviceType, WorkoutProgramDayModel day) {
    // Egzersiz ID'lerini al
    final exerciseIds = day.exercises.map((e) => e.workoutProgramExerciseID).toList();

    // Bu günün yapılması gereken gün olup olmadığını kontrol et
    final nextWorkoutDayId = _findNextWorkoutDay();
    final isNextWorkoutDay = !day.isRestDay && day.workoutProgramDayID == nextWorkoutDayId;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: theme.colorScheme.surface,
        border: isNextWorkoutDay
          ? Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.4),
              width: 2,
            )
          : Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
          if (isNextWorkoutDay)
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
              spreadRadius: 1,
            ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // "Bugünkü Antrenman" etiketi (sadece yapılması gereken günde)
              if (isNextWorkoutDay) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    border: Border.all(
                      color: theme.colorScheme.primary.withValues(alpha: 0.3),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.schedule,
                        color: theme.colorScheme.primary,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Bugünkü Antrenman',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
              ],

              // Gün başlığı
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: day.isRestDay
                        ? theme.colorScheme.secondary.withValues(alpha: 0.1)
                        : theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: day.isRestDay
                          ? theme.colorScheme.secondary.withValues(alpha: 0.2)
                          : theme.colorScheme.primary.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        day.dayIcon,
                        style: TextStyle(
                          fontSize: 24,
                          color: day.isRestDay
                            ? theme.colorScheme.secondary
                            : theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                  ResponsiveSpacing.horizontal(
                    mobile: 8.0,
                    tablet: 10.0,
                    desktop: 12.0,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          day.dayName,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          day.isRestDay ? 'Dinlenme Günü' : 'Antrenman Günü',
                          style: TextStyle(
                            fontSize: 13,
                            color: theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Gün numarası
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: day.isRestDay
                            ? theme.colorScheme.secondary.withValues(alpha: 0.15)
                            : theme.colorScheme.primary.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: day.isRestDay
                              ? theme.colorScheme.secondary.withValues(alpha: 0.3)
                              : theme.colorScheme.primary.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'GÜN ${day.dayNumber}',
                          style: TextStyle(
                            color: day.isRestDay
                              ? theme.colorScheme.secondary
                              : theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),


                    ],
                  ),
                ],
              ),

              if (!day.isRestDay && day.exercises.isNotEmpty) ...[
                ResponsiveSpacing.vertical(
                  mobile: 12.0,
                  tablet: 16.0,
                  desktop: 20.0,
                ),

                // Egzersizler
                ...day.exercises.map((exercise) => Padding(
                  padding: EdgeInsets.only(
                    bottom: AppSpacing.responsive(context,
                      mobile: 8.0,
                      tablet: 10.0,
                      desktop: 12.0,
                    ),
                  ),
                  child: ExerciseRowWidget(
                    programId: widget.memberWorkoutProgramId,
                    dayId: day.workoutProgramDayID,
                    exerciseId: exercise.workoutProgramExerciseID,
                    exerciseName: exercise.exerciseName,
                    sets: exercise.sets,
                    reps: exercise.reps.toString(),
                    notes: exercise.notes,
                    allExerciseIds: exerciseIds,
                  ),
                )),

                // Antrenman Bitir Butonu (sadece antrenman günlerinde)
                ResponsiveSpacing.vertical(
                  mobile: 16.0,
                  tablet: 20.0,
                  desktop: 24.0,
                ),

                CompleteWorkoutButton(
                  programId: widget.memberWorkoutProgramId,
                  dayId: day.workoutProgramDayID,
                  dayName: day.dayName,
                  allExerciseIds: exerciseIds,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Modern Program Header
  Widget _buildProgramHeader(ThemeData theme, DeviceType deviceType, dynamic programDetail) {
    return Container(
      margin: EdgeInsets.only(
        top: AppSpacing.responsive(context,
          mobile: 60.0,  // Space for fixed back button
          tablet: 70.0,
          desktop: 80.0,
        ),
      ),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Program Title & Stats Row
          Row(
            children: [
              // Program Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [theme.colorScheme.primary, theme.colorScheme.secondary],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.fitness_center,
                  color: Colors.white,
                  size: 30,
                ),
              ),

              const SizedBox(width: 16),

              // Program Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.programName,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${programDetail.days.length} Günlük Program',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              // Quick Stats
              _buildQuickStats(programDetail),
            ],
          ),

          const SizedBox(height: 16),

          // Progress Bar
          _buildProgressBar(programDetail),
        ],
      ),
    );
  }

  /// Quick Stats Widget
  Widget _buildQuickStats(dynamic programDetail) {
    final workoutDays = programDetail.days.where((day) => !day.isRestDay).length;
    final restDays = programDetail.days.length - workoutDays;

    return Row(
      children: [
        _buildStatItem(Icons.fitness_center, workoutDays.toString(), 'Antrenman'),
        const SizedBox(width: 12),
        _buildStatItem(Icons.hotel, restDays.toString(), 'Dinlenme'),
      ],
    );
  }

  /// Stat Item Widget
  Widget _buildStatItem(IconData icon, String value, String label) {
    final theme = Theme.of(context);
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 16, color: theme.colorScheme.primary),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// Progress Bar Widget
  Widget _buildProgressBar(dynamic programDetail) {
    final theme = Theme.of(context);
    // Tamamlanan günleri hesapla
    int completedDays = 0;
    final workoutDays = programDetail.days.where((day) => !day.isRestDay).toList();

    for (final day in workoutDays) {
      final sessionKey = '${widget.memberWorkoutProgramId}_${day.workoutProgramDayID}';
      final isCompleted = ref.read(workoutSessionCompletionProvider(sessionKey));
      if (isCompleted) completedDays++;
    }

    final progress = workoutDays.isEmpty ? 0.0 : completedDays / workoutDays.length;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'İlerleme',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            Text(
              '$completedDays/${workoutDays.length} Tamamlandı',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [theme.colorScheme.primary, theme.colorScheme.tertiary],
                ),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Sabit pozisyonda geri dön butonu
  Widget _buildFixedBackButton(ThemeData theme, DeviceType deviceType) {
    return Positioned(
      top: AppSpacing.responsive(context,
        mobile: 16.0,
        tablet: 20.0,
        desktop: 24.0,
      ),
      left: AppSpacing.responsive(context,
        mobile: 16.0,
        tablet: 20.0,
        desktop: 24.0,
      ),
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
            mobile: 20.0,
            tablet: 24.0,
            desktop: 28.0,
          )),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: AppSpacing.responsive(context,
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => Navigator.of(context).pop(),
            borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
              mobile: 20.0,
              tablet: 24.0,
              desktop: 28.0,
            )),
            child: SizedBox(
              width: AppSpacing.responsive(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              height: AppSpacing.responsive(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              child: Center(
                child: Icon(
                  Icons.arrow_back,
                  color: theme.colorScheme.onSurface,
                  size: AppSpacing.responsiveIconSize(context,
                    mobile: 20.0,
                    tablet: 24.0,
                    desktop: 28.0,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

}
