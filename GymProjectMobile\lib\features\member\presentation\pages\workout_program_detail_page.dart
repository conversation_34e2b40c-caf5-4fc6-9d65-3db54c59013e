/// Workout Program Detail Page - GymKod Pro Mobile
///
/// Bu sayfa antrenman programının detayını gösterir.
/// <PERSON><PERSON><PERSON><PERSON>, egzersizler, set/tekrar bilgileri
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../providers/workout_program_detail_provider.dart';
import '../providers/workout_progress_provider.dart';
import '../widgets/day_completion_checkbox.dart';
import '../widgets/program_reset_dropdown.dart';
import '../widgets/cycle_indicator.dart';
import '../widgets/progress_indicator_widget.dart';

class WorkoutProgramDetailPage extends ConsumerStatefulWidget {
  final int memberWorkoutProgramId;
  final String programName;

  const WorkoutProgramDetailPage({
    super.key,
    required this.memberWorkoutProgramId,
    required this.programName,
  });

  @override
  ConsumerState<WorkoutProgramDetailPage> createState() => _WorkoutProgramDetailPageState();
}

class _WorkoutProgramDetailPageState extends ConsumerState<WorkoutProgramDetailPage> {
  @override
  void initState() {
    super.initState();

    // Sayfa açıldığında program detayını ve ilerleme verilerini yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(workoutProgramDetailProvider.notifier).loadProgramDetail(widget.memberWorkoutProgramId);
      ref.read(workoutProgressProvider.notifier).loadProgramProgress(widget.memberWorkoutProgramId);
      LoggingService.info('Workout program detail page loaded', tag: 'WORKOUT_DETAIL');
    });
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        final theme = Theme.of(context);

        return Scaffold(
          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: Stack(
                  children: [
                    // Main Content
                    Column(
                      children: [
                        // Responsive Content Section
                        Expanded(
                          child: Padding(
                            padding: AppSpacing.responsiveScreenPadding(context),
                            child: _buildResponsiveProgramDetailContent(theme, deviceType),
                          ),
                        ),
                      ],
                    ),

                    // Fixed Header (Back Button + Reset Dropdown + Cycle Indicator)
                    _buildFixedHeader(theme, deviceType),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Responsive Program Detail Content
  Widget _buildResponsiveProgramDetailContent(ThemeData theme, DeviceType deviceType) {
    final isLoading = ref.watch(workoutProgramDetailLoadingProvider);
    final isRefreshing = ref.watch(workoutProgramDetailRefreshingProvider);
    final error = ref.watch(workoutProgramDetailErrorProvider);
    final programDetail = ref.watch(workoutProgramDetailDataProvider);

    if (isLoading && !isRefreshing) {
      return _buildLoadingState(theme, deviceType);
    }

    if (error != null) {
      return _buildErrorState(theme, deviceType, error);
    }

    if (programDetail == null) {
      return _buildNoProgramState(theme, deviceType);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(workoutProgramDetailProvider.notifier).refreshProgramDetail(widget.memberWorkoutProgramId);
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Top spacing for fixed back button
            SizedBox(
              height: AppSpacing.responsive(context,
                mobile: 60.0,  // Space for fixed back button
                tablet: 70.0,
                desktop: 80.0,
              ),
            ),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // İlerleme göstergesi
            ProgressIndicatorWidget(
              programId: widget.memberWorkoutProgramId,
              totalWorkoutDays: programDetail.days.where((day) => !day.isRestDay).length,
              programName: widget.programName,
            ),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Program günleri (dayNumber'a göre sıralı)
            ...(programDetail.days.toList()
                ..sort((a, b) => a.dayNumber.compareTo(b.dayNumber)))
                .map((day) => Padding(
                  padding: EdgeInsets.only(
                    bottom: AppSpacing.responsive(context,
                      mobile: 12.0,
                      tablet: 16.0,
                      desktop: 20.0,
                    ),
                  ),
                  child: _buildWorkoutDayCard(theme, deviceType, day),
                )),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
          ],
        ),
      ),
    );
  }

  /// Loading durumu
  Widget _buildLoadingState(ThemeData theme, DeviceType deviceType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ResponsiveText(
            'Program detayı yükleniyor...',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Hata durumu
  Widget _buildErrorState(ThemeData theme, DeviceType deviceType, String error) {
    return Center(
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              color: theme.colorScheme.error,
            ),
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            ResponsiveText(
              'Hata Oluştu',
              textType: 'h3',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 10.0,
              desktop: 12.0,
            ),
            ResponsiveText(
              error,
              textType: 'bodymedium',
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
            ElevatedButton(
              onPressed: () {
                ref.read(workoutProgramDetailProvider.notifier).loadProgramDetail(widget.memberWorkoutProgramId);
              },
              child: const ResponsiveText(
                'Tekrar Dene',
                textType: 'button',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Program yok durumu
  Widget _buildNoProgramState(ThemeData theme, DeviceType deviceType) {
    return Center(
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.fitness_center_outlined,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              color: theme.colorScheme.primary,
            ),
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            ResponsiveText(
              'Program Bulunamadı',
              textType: 'h3',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 10.0,
              desktop: 12.0,
            ),
            ResponsiveText(
              'Bu program detayına erişiminiz bulunmuyor veya program silinmiş olabilir.',
              textType: 'bodymedium',
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const ResponsiveText(
                'Geri Dön',
                textType: 'button',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Antrenman günü kartı
  Widget _buildWorkoutDayCard(ThemeData theme, DeviceType deviceType, WorkoutProgramDayModel day) {
    // Gün tamamlanma durumunu kontrol et
    final dayKey = '${widget.memberWorkoutProgramId}_${day.workoutProgramDayID}';
    final isCompleted = ref.watch(dayCompletionProvider(dayKey));

    return Container(
      padding: AppSpacing.responsiveCardPadding(context),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
          mobile: 12.0,
          tablet: 16.0,
          desktop: 20.0,
        )),
        border: Border.all(
          color: isCompleted && !day.isRestDay
            ? Colors.green.withValues(alpha: 0.5)
            : theme.colorScheme.outline.withValues(alpha: 0.2),
          width: isCompleted && !day.isRestDay ? 2 : 1,
        ),
        color: isCompleted && !day.isRestDay
          ? Colors.green.withValues(alpha: 0.05)
          : theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Gün başlığı
          Row(
            children: [
              Text(
                day.dayIcon,
                style: TextStyle(
                  fontSize: AppSpacing.responsiveIconSize(context,
                    mobile: 28.0,
                    tablet: 32.0,
                    desktop: 36.0,
                  ),
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      day.dayName,
                      textType: 'h3',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    ResponsiveText(
                      day.isRestDay ? 'Dinlenme Günü' : 'Antrenman Günü',
                      textType: 'bodysmall',
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  // Gün numarası
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.responsive(context,
                        mobile: 8.0,
                        tablet: 10.0,
                        desktop: 12.0,
                      ),
                      vertical: AppSpacing.responsive(context,
                        mobile: 4.0,
                        tablet: 6.0,
                        desktop: 8.0,
                      ),
                    ),
                    decoration: BoxDecoration(
                      color: day.isRestDay
                        ? theme.colorScheme.secondary.withValues(alpha: 0.2)
                        : theme.colorScheme.primary.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                        mobile: 12.0,
                        tablet: 14.0,
                        desktop: 16.0,
                      )),
                    ),
                    child: ResponsiveText(
                      'Gün ${day.dayNumber}',
                      textType: 'bodysmall',
                      style: TextStyle(
                        color: day.isRestDay
                          ? theme.colorScheme.secondary
                          : theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // Checkbox (sadece antrenman günlerinde)
                  if (!day.isRestDay) ...[
                    const SizedBox(width: 8),
                    ResponsiveDayCompletionCheckbox(
                      programId: widget.memberWorkoutProgramId,
                      dayId: day.workoutProgramDayID,
                      dayNumber: day.dayNumber,
                      isRestDay: day.isRestDay,
                      allWorkoutDayIds: _getAllWorkoutDayIds(),
                    ),
                  ],
                ],
              ),
            ],
          ),

          if (!day.isRestDay && day.exercises.isNotEmpty) ...[
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),

            // Egzersizler
            ...day.exercises.map((exercise) => Padding(
              padding: EdgeInsets.only(
                bottom: AppSpacing.responsive(context,
                  mobile: 8.0,
                  tablet: 10.0,
                  desktop: 12.0,
                ),
              ),
              child: _buildExerciseItem(theme, deviceType, exercise),
            )),
          ],
        ],
      ),
    );
  }

  /// Egzersiz item'ı
  Widget _buildExerciseItem(ThemeData theme, DeviceType deviceType, WorkoutProgramExerciseModel exercise) {
    return Container(
      padding: AppSpacing.responsiveCardPadding(context),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
          mobile: 8.0,
          tablet: 10.0,
          desktop: 12.0,
        )),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Egzersiz başlığı
          Row(
            children: [
              Text(
                exercise.categoryIcon,
                style: TextStyle(
                  fontSize: AppSpacing.responsiveIconSize(context,
                    mobile: 20.0,
                    tablet: 24.0,
                    desktop: 28.0,
                  ),
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 6.0,
                tablet: 8.0,
                desktop: 10.0,
              ),
              Expanded(
                child: ResponsiveText(
                  exercise.exerciseName,
                  textType: 'h4',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (exercise.categoryName != null)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.responsive(context,
                      mobile: 6.0,
                      tablet: 8.0,
                      desktop: 10.0,
                    ),
                    vertical: AppSpacing.responsive(context,
                      mobile: 2.0,
                      tablet: 3.0,
                      desktop: 4.0,
                    ),
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                      mobile: 8.0,
                      tablet: 10.0,
                      desktop: 12.0,
                    )),
                  ),
                  child: ResponsiveText(
                    exercise.categoryName!,
                    textType: 'bodysmall',
                    style: TextStyle(
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
            ],
          ),

          ResponsiveSpacing.vertical(
            mobile: 8.0,
            tablet: 10.0,
            desktop: 12.0,
          ),

          // Set/Tekrar bilgileri
          Row(
            children: [
              Expanded(
                child: _buildExerciseInfo(
                  theme,
                  deviceType,
                  Icons.fitness_center,
                  '${exercise.sets} Set',
                ),
              ),
              Expanded(
                child: _buildExerciseInfo(
                  theme,
                  deviceType,
                  Icons.repeat,
                  '${exercise.reps} Tekrar',
                ),
              ),
              if (exercise.restTime != null)
                Expanded(
                  child: _buildExerciseInfo(
                    theme,
                    deviceType,
                    Icons.timer,
                    exercise.restTimeFormatted,
                  ),
                ),
            ],
          ),

          // Egzersiz açıklaması
          if (exercise.exerciseDescription != null && exercise.exerciseDescription!.isNotEmpty) ...[
            ResponsiveSpacing.vertical(
              mobile: 6.0,
              tablet: 8.0,
              desktop: 10.0,
            ),
            ResponsiveText(
              exercise.exerciseDescription!,
              textType: 'bodymedium',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],

          // Egzersiz notları
          if (exercise.notes != null && exercise.notes!.isNotEmpty) ...[
            ResponsiveSpacing.vertical(
              mobile: 6.0,
              tablet: 8.0,
              desktop: 10.0,
            ),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(AppSpacing.responsive(context,
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              )),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                  mobile: 6.0,
                  tablet: 8.0,
                  desktop: 10.0,
                )),
              ),
              child: ResponsiveText(
                'Not: ${exercise.notes!}',
                textType: 'bodymedium',
                style: TextStyle(
                  color: theme.colorScheme.onSecondaryContainer,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Egzersiz bilgi item'ı
  Widget _buildExerciseInfo(ThemeData theme, DeviceType deviceType, IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: AppSpacing.responsiveIconSize(context,
            mobile: 18.0,
            tablet: 20.0,
            desktop: 22.0,
          ),
          color: theme.colorScheme.primary,
        ),
        ResponsiveSpacing.horizontal(
          mobile: 4.0,
          tablet: 6.0,
          desktop: 8.0,
        ),
        Flexible(
          child: ResponsiveText(
            text,
            textType: 'bodymedium',
          ),
        ),
      ],
    );
  }

  /// Tüm antrenman günlerinin ID'lerini al
  List<int> _getAllWorkoutDayIds() {
    final programDetail = ref.read(workoutProgramDetailDataProvider);
    if (programDetail == null) return [];

    return programDetail.days
        .where((day) => !day.isRestDay)
        .map((day) => day.workoutProgramDayID)
        .toList();
  }

  /// Sabit pozisyonda header (geri dön butonu + reset dropdown + cycle indicator)
  Widget _buildFixedHeader(ThemeData theme, DeviceType deviceType) {
    return Positioned(
      top: AppSpacing.responsive(context,
        mobile: 16.0,
        tablet: 20.0,
        desktop: 24.0,
      ),
      left: AppSpacing.responsive(context,
        mobile: 16.0,
        tablet: 20.0,
        desktop: 24.0,
      ),
      right: AppSpacing.responsive(context,
        mobile: 16.0,
        tablet: 20.0,
        desktop: 24.0,
      ),
      child: Row(
        children: [
          // Geri dön butonu
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surface.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                mobile: 20.0,
                tablet: 24.0,
                desktop: 28.0,
              )),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: AppSpacing.responsive(context,
                    mobile: 8.0,
                    tablet: 10.0,
                    desktop: 12.0,
                  ),
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Navigator.of(context).pop(),
                borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                  mobile: 20.0,
                  tablet: 24.0,
                  desktop: 28.0,
                )),
                child: SizedBox(
                  width: AppSpacing.responsive(context,
                    mobile: 40.0,
                    tablet: 48.0,
                    desktop: 56.0,
                  ),
                  height: AppSpacing.responsive(context,
                    mobile: 40.0,
                    tablet: 48.0,
                    desktop: 56.0,
                  ),
                  child: Center(
                    child: Icon(
                      Icons.arrow_back,
                      color: theme.colorScheme.onSurface,
                      size: AppSpacing.responsiveIconSize(context,
                        mobile: 20.0,
                        tablet: 24.0,
                        desktop: 28.0,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          const Spacer(),

          // Cycle Indicator
          ResponsiveCycleIndicator(
            programId: widget.memberWorkoutProgramId,
            animated: true,
          ),

          const SizedBox(width: 8),

          // Reset Dropdown
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surface.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                mobile: 20.0,
                tablet: 24.0,
                desktop: 28.0,
              )),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: AppSpacing.responsive(context,
                    mobile: 8.0,
                    tablet: 10.0,
                    desktop: 12.0,
                  ),
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ProgramResetDropdown(
              programId: widget.memberWorkoutProgramId,
              programName: widget.programName,
              iconSize: AppSpacing.responsiveIconSize(context,
                mobile: 20.0,
                tablet: 24.0,
                desktop: 28.0,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
