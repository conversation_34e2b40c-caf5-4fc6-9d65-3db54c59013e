/// Workout Program Detail Page - GymKod Pro Mobile
///
/// Bu sayfa antrenman programının detayını gösterir.
/// <PERSON><PERSON><PERSON><PERSON>, egzersizler, set/tekrar bilgileri
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../providers/workout_program_detail_provider.dart';
import '../providers/exercise_progress_provider.dart';
import '../widgets/exercise_completion_checkbox.dart';
import '../widgets/complete_workout_button.dart';

class WorkoutProgramDetailPage extends ConsumerStatefulWidget {
  final int memberWorkoutProgramId;
  final String programName;

  const WorkoutProgramDetailPage({
    super.key,
    required this.memberWorkoutProgramId,
    required this.programName,
  });

  @override
  ConsumerState<WorkoutProgramDetailPage> createState() => _WorkoutProgramDetailPageState();
}

class _WorkoutProgramDetailPageState extends ConsumerState<WorkoutProgramDetailPage> {
  @override
  void initState() {
    super.initState();

    // Sayfa açıldığında program detayını yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(workoutProgramDetailProvider.notifier).loadProgramDetail(widget.memberWorkoutProgramId);

      // Program detayı yüklendikten sonra egzersiz progress'lerini yükle
      Future.delayed(const Duration(milliseconds: 500), () {
        final programDetail = ref.read(workoutProgramDetailDataProvider);
        if (programDetail != null) {
          for (final day in programDetail.days) {
            if (!day.isRestDay && day.exercises.isNotEmpty) {
              final exerciseIds = day.exercises.map((e) => e.workoutProgramExerciseID).toList();
              ref.read(exerciseProgressProvider.notifier).loadDayExercises(
                widget.memberWorkoutProgramId,
                day.workoutProgramDayID,
                exerciseIds,
              );
            }
          }
        }
      });

      LoggingService.info('Workout program detail page loaded', tag: 'WORKOUT_DETAIL');
    });
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        final theme = Theme.of(context);

        return Scaffold(
          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: Stack(
                  children: [
                    // Main Content
                    Column(
                      children: [
                        // Responsive Content Section
                        Expanded(
                          child: Padding(
                            padding: AppSpacing.responsiveScreenPadding(context),
                            child: _buildResponsiveProgramDetailContent(theme, deviceType),
                          ),
                        ),
                      ],
                    ),

                    // Fixed Back Button
                    _buildFixedBackButton(theme, deviceType),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Responsive Program Detail Content
  Widget _buildResponsiveProgramDetailContent(ThemeData theme, DeviceType deviceType) {
    final isLoading = ref.watch(workoutProgramDetailLoadingProvider);
    final isRefreshing = ref.watch(workoutProgramDetailRefreshingProvider);
    final error = ref.watch(workoutProgramDetailErrorProvider);
    final programDetail = ref.watch(workoutProgramDetailDataProvider);

    if (isLoading && !isRefreshing) {
      return _buildLoadingState(theme, deviceType);
    }

    if (error != null) {
      return _buildErrorState(theme, deviceType, error);
    }

    if (programDetail == null) {
      return _buildNoProgramState(theme, deviceType);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(workoutProgramDetailProvider.notifier).refreshProgramDetail(widget.memberWorkoutProgramId);
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Top spacing for fixed back button
            SizedBox(
              height: AppSpacing.responsive(context,
                mobile: 60.0,  // Space for fixed back button
                tablet: 70.0,
                desktop: 80.0,
              ),
            ),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Basit ilerleme bilgisi
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(Icons.fitness_center, color: theme.colorScheme.primary),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Egzersizleri tamamlayın ve "Antrenmanı Bitir" butonuna basın',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Program günleri (dayNumber'a göre sıralı)
            ...(programDetail.days.toList()
                ..sort((a, b) => a.dayNumber.compareTo(b.dayNumber)))
                .map((day) => Padding(
                  padding: EdgeInsets.only(
                    bottom: AppSpacing.responsive(context,
                      mobile: 12.0,
                      tablet: 16.0,
                      desktop: 20.0,
                    ),
                  ),
                  child: _buildWorkoutDayCard(theme, deviceType, day),
                )),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
          ],
        ),
      ),
    );
  }

  /// Loading durumu
  Widget _buildLoadingState(ThemeData theme, DeviceType deviceType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ResponsiveText(
            'Program detayı yükleniyor...',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Hata durumu
  Widget _buildErrorState(ThemeData theme, DeviceType deviceType, String error) {
    return Center(
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              color: theme.colorScheme.error,
            ),
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            ResponsiveText(
              'Hata Oluştu',
              textType: 'h3',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 10.0,
              desktop: 12.0,
            ),
            ResponsiveText(
              error,
              textType: 'bodymedium',
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
            ElevatedButton(
              onPressed: () {
                ref.read(workoutProgramDetailProvider.notifier).loadProgramDetail(widget.memberWorkoutProgramId);
              },
              child: const ResponsiveText(
                'Tekrar Dene',
                textType: 'button',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Program yok durumu
  Widget _buildNoProgramState(ThemeData theme, DeviceType deviceType) {
    return Center(
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.fitness_center_outlined,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              color: theme.colorScheme.primary,
            ),
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            ResponsiveText(
              'Program Bulunamadı',
              textType: 'h3',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 10.0,
              desktop: 12.0,
            ),
            ResponsiveText(
              'Bu program detayına erişiminiz bulunmuyor veya program silinmiş olabilir.',
              textType: 'bodymedium',
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const ResponsiveText(
                'Geri Dön',
                textType: 'button',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Antrenman günü kartı
  Widget _buildWorkoutDayCard(ThemeData theme, DeviceType deviceType, WorkoutProgramDayModel day) {
    // Egzersiz ID'lerini al
    final exerciseIds = day.exercises.map((e) => e.workoutProgramExerciseID).toList();

    return Container(
      padding: AppSpacing.responsiveCardPadding(context),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
          mobile: 12.0,
          tablet: 16.0,
          desktop: 20.0,
        )),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Gün başlığı
          Row(
            children: [
              Text(
                day.dayIcon,
                style: TextStyle(
                  fontSize: AppSpacing.responsiveIconSize(context,
                    mobile: 28.0,
                    tablet: 32.0,
                    desktop: 36.0,
                  ),
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      day.dayName,
                      textType: 'h3',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    ResponsiveText(
                      day.isRestDay ? 'Dinlenme Günü' : 'Antrenman Günü',
                      textType: 'bodysmall',
                    ),
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Gün numarası
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.responsive(context,
                        mobile: 8.0,
                        tablet: 10.0,
                        desktop: 12.0,
                      ),
                      vertical: AppSpacing.responsive(context,
                        mobile: 4.0,
                        tablet: 6.0,
                        desktop: 8.0,
                      ),
                    ),
                    decoration: BoxDecoration(
                      color: day.isRestDay
                        ? theme.colorScheme.secondary.withValues(alpha: 0.2)
                        : theme.colorScheme.primary.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                        mobile: 12.0,
                        tablet: 14.0,
                        desktop: 16.0,
                      )),
                    ),
                    child: ResponsiveText(
                      'Gün ${day.dayNumber}',
                      textType: 'bodysmall',
                      style: TextStyle(
                        color: day.isRestDay
                          ? theme.colorScheme.secondary
                          : theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // Antrenman seansı durumu (sadece antrenman günlerinde)
                  if (!day.isRestDay)
                    Consumer(
                      builder: (context, ref, child) {
                        final sessionKey = '${widget.memberWorkoutProgramId}_${day.workoutProgramDayID}';
                        final isSessionCompleted = ref.watch(workoutSessionCompletionProvider(sessionKey));

                        if (isSessionCompleted) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.check_circle, color: Colors.green, size: 16),
                                const SizedBox(width: 4),
                                Text(
                                  'Tamamlandı',
                                  style: TextStyle(
                                    color: Colors.green,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                ],
              ),
            ],
          ),

          if (!day.isRestDay && day.exercises.isNotEmpty) ...[
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),

            // Egzersizler
            ...day.exercises.map((exercise) => Padding(
              padding: EdgeInsets.only(
                bottom: AppSpacing.responsive(context,
                  mobile: 8.0,
                  tablet: 10.0,
                  desktop: 12.0,
                ),
              ),
              child: ExerciseRowWidget(
                programId: widget.memberWorkoutProgramId,
                dayId: day.workoutProgramDayID,
                exerciseId: exercise.workoutProgramExerciseID,
                exerciseName: exercise.exerciseName,
                sets: exercise.sets,
                reps: exercise.reps.toString(),
                notes: exercise.notes,
                allExerciseIds: exerciseIds,
              ),
            )),

            // Antrenman Bitir Butonu (sadece antrenman günlerinde)
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            CompleteWorkoutButton(
              programId: widget.memberWorkoutProgramId,
              dayId: day.workoutProgramDayID,
              dayName: day.dayName,
              allExerciseIds: exerciseIds,
            ),
          ],
        ],
      ),
    );
  }

  /// Sabit pozisyonda geri dön butonu
  Widget _buildFixedBackButton(ThemeData theme, DeviceType deviceType) {
    return Positioned(
      top: AppSpacing.responsive(context,
        mobile: 16.0,
        tablet: 20.0,
        desktop: 24.0,
      ),
      left: AppSpacing.responsive(context,
        mobile: 16.0,
        tablet: 20.0,
        desktop: 24.0,
      ),
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
            mobile: 20.0,
            tablet: 24.0,
            desktop: 28.0,
          )),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: AppSpacing.responsive(context,
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => Navigator.of(context).pop(),
            borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
              mobile: 20.0,
              tablet: 24.0,
              desktop: 28.0,
            )),
            child: SizedBox(
              width: AppSpacing.responsive(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              height: AppSpacing.responsive(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              child: Center(
                child: Icon(
                  Icons.arrow_back,
                  color: theme.colorScheme.onSurface,
                  size: AppSpacing.responsiveIconSize(context,
                    mobile: 20.0,
                    tablet: 24.0,
                    desktop: 28.0,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

}
