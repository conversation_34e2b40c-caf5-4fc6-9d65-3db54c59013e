/// Local Exercise Progress Service - GymKod Pro Mobile
///
/// Bu service egzersiz bazlı ilerleme takibi yapar.
/// Gerçekçi antrenman deneyimi için egzersiz tamamlama sistemi.
library;

import 'package:shared_preferences/shared_preferences.dart';

/// Lokal egzersiz ilerleme servisi
class LocalExerciseProgressService {
  // SharedPreferences anahtarları
  static const String _exercisePrefix = 'exercise_';
  static const String _workoutPrefix = 'workout_';
  static const String _statsPrefix = 'stats_';
  
  // Spam koruması için cooldown süresi
  static const Duration _cooldownDuration = Duration(seconds: 1);
  static final Map<String, DateTime> _lastClickTimes = {};

  /// Egzersiz tamamlama durumunu değiştir (toggle)
  static Future<bool> toggleExerciseCompletion(
    int programId, 
    int dayId, 
    int exerciseId
  ) async {
    // Spam koruması kontrolü
    if (!_canToggleExercise(programId, dayId, exerciseId)) {
      return false;
    }
    
    _recordExerciseClick(programId, dayId, exerciseId);
    
    final prefs = await SharedPreferences.getInstance();
    final exerciseKey = '$_exercisePrefix${programId}_${dayId}_$exerciseId';
    final dateKey = '${exerciseKey}_date';
    
    // Mevcut durumu al
    final currentStatus = prefs.getBool(exerciseKey) ?? false;
    final newStatus = !currentStatus;
    
    // Yeni durumu kaydet
    await prefs.setBool(exerciseKey, newStatus);
    
    if (newStatus) {
      // Tamamlandıysa tarihi kaydet
      await prefs.setString(dateKey, DateTime.now().toIso8601String());
    } else {
      // Tamamlama iptal edildiyse tarihi sil
      await prefs.remove(dateKey);
    }
    
    // Son aktivite tarihini güncelle
    await _updateLastActivity(programId);
    
    return newStatus;
  }

  /// Belirli bir egzersizin tamamlanıp tamamlanmadığını kontrol et
  static Future<bool> isExerciseCompleted(
    int programId, 
    int dayId, 
    int exerciseId
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final exerciseKey = '$_exercisePrefix${programId}_${dayId}_$exerciseId';
    return prefs.getBool(exerciseKey) ?? false;
  }

  /// Belirli bir günün tüm egzersizlerinin durumunu al
  static Future<Map<int, bool>> getDayExercisesStatus(
    int programId, 
    int dayId, 
    List<int> exerciseIds
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final result = <int, bool>{};
    
    for (final exerciseId in exerciseIds) {
      final exerciseKey = '$_exercisePrefix${programId}_${dayId}_$exerciseId';
      result[exerciseId] = prefs.getBool(exerciseKey) ?? false;
    }
    
    return result;
  }

  /// Belirli bir günün tüm egzersizlerinin tamamlanıp tamamlanmadığını kontrol et
  static Future<bool> isDayCompleted(
    int programId, 
    int dayId, 
    List<int> exerciseIds
  ) async {
    if (exerciseIds.isEmpty) return false;
    
    final exercisesStatus = await getDayExercisesStatus(programId, dayId, exerciseIds);
    return exercisesStatus.values.every((completed) => completed);
  }

  /// Antrenman seansını tamamla
  static Future<void> completeWorkoutSession(
    int programId, 
    int dayId
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionKey = '$_workoutPrefix${programId}_${dayId}_completed';
    final sessionDateKey = '$_workoutPrefix${programId}_${dayId}_date';
    
    await prefs.setBool(sessionKey, true);
    await prefs.setString(sessionDateKey, DateTime.now().toIso8601String());
    
    // İstatistikleri güncelle
    await _incrementTotalWorkouts(programId);
    await _updateLastActivity(programId);
  }

  /// Antrenman seansının tamamlanıp tamamlanmadığını kontrol et
  static Future<bool> isWorkoutSessionCompleted(
    int programId, 
    int dayId
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionKey = '$_workoutPrefix${programId}_${dayId}_completed';
    return prefs.getBool(sessionKey) ?? false;
  }

  /// Program istatistiklerini al
  static Future<Map<String, dynamic>> getProgramStats(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    
    final totalWorkouts = prefs.getInt('$_statsPrefix${programId}_total_workouts') ?? 0;
    final lastActivityStr = prefs.getString('$_statsPrefix${programId}_last_activity');
    final firstStartStr = prefs.getString('$_statsPrefix${programId}_first_start');
    
    return {
      'totalWorkouts': totalWorkouts,
      'lastActivity': lastActivityStr != null ? DateTime.tryParse(lastActivityStr) : null,
      'firstStart': firstStartStr != null ? DateTime.tryParse(firstStartStr) : null,
    };
  }

  /// Belirli bir günün egzersizlerini sıfırla
  static Future<void> resetDayExercises(
    int programId, 
    int dayId, 
    List<int> exerciseIds
  ) async {
    final prefs = await SharedPreferences.getInstance();
    
    for (final exerciseId in exerciseIds) {
      final exerciseKey = '$_exercisePrefix${programId}_${dayId}_$exerciseId';
      final dateKey = '${exerciseKey}_date';
      
      await prefs.remove(exerciseKey);
      await prefs.remove(dateKey);
    }
    
    // Antrenman seansını da sıfırla
    final sessionKey = '$_workoutPrefix${programId}_${dayId}_completed';
    final sessionDateKey = '$_workoutPrefix${programId}_${dayId}_date';
    
    await prefs.remove(sessionKey);
    await prefs.remove(sessionDateKey);
    
    await _updateLastActivity(programId);
  }

  /// Tüm program verilerini temizle
  static Future<void> clearProgramData(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    
    final keys = prefs.getKeys();
    final programKeys = keys.where((key) => 
      key.startsWith('$_exercisePrefix$programId') ||
      key.startsWith('$_workoutPrefix$programId') ||
      key.startsWith('$_statsPrefix$programId')
    );
    
    for (final key in programKeys) {
      await prefs.remove(key);
    }
  }

  /// Tüm ilerleme verilerini temizle
  static Future<void> clearAllProgressData() async {
    final prefs = await SharedPreferences.getInstance();
    
    final keys = prefs.getKeys();
    final progressKeys = keys.where((key) => 
      key.startsWith(_exercisePrefix) ||
      key.startsWith(_workoutPrefix) ||
      key.startsWith(_statsPrefix)
    );
    
    for (final key in progressKeys) {
      await prefs.remove(key);
    }
  }

  // Private helper methods

  /// Spam koruması - belirli süre içinde aynı egzersize tıklamayı engelle
  static bool _canToggleExercise(int programId, int dayId, int exerciseId) {
    final key = '${programId}_${dayId}_$exerciseId';
    final lastClick = _lastClickTimes[key];
    
    if (lastClick == null) return true;
    
    final timeSinceLastClick = DateTime.now().difference(lastClick);
    return timeSinceLastClick >= _cooldownDuration;
  }

  /// Egzersiz tıklama zamanını kaydet
  static void _recordExerciseClick(int programId, int dayId, int exerciseId) {
    final key = '${programId}_${dayId}_$exerciseId';
    _lastClickTimes[key] = DateTime.now();
  }

  /// Toplam antrenman sayısını artır
  static Future<void> _incrementTotalWorkouts(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    final key = '$_statsPrefix${programId}_total_workouts';
    final current = prefs.getInt(key) ?? 0;
    await prefs.setInt(key, current + 1);
  }

  /// Son aktivite tarihini güncelle
  static Future<void> _updateLastActivity(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('$_statsPrefix${programId}_last_activity', 
                         DateTime.now().toIso8601String());
    
    // İlk başlangıç tarihini de kaydet (eğer yoksa)
    final firstStartKey = '$_statsPrefix${programId}_first_start';
    if (!prefs.containsKey(firstStartKey)) {
      await prefs.setString(firstStartKey, DateTime.now().toIso8601String());
    }
  }
}
