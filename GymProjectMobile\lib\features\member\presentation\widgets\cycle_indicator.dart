/// Cycle Indicator Widget - GymKod Pro Mobile
///
/// Bu widget mevcut döngü sayısını gösterir.
/// 1. döngü<PERSON> görünmez, 2. döngüden itibaren görünür.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/workout_progress_provider.dart';

/// Döngü göstergesi widget'ı
class CycleIndicator extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Özel stil
  final CycleIndicatorStyle? style;
  
  /// Animasyon süresi
  final Duration animationDuration;

  const CycleIndicator({
    super.key,
    required this.programId,
    this.style,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final progress = ref.watch(programProgressProvider(programId));
    
    // İlerleme verisi yoksa veya 1. döngüdeyse gösterme
    if (progress == null || progress.currentCycle <= 1) {
      return const SizedBox.shrink();
    }

    final effectiveStyle = style ?? CycleIndicatorStyle.defaultStyle(theme);

    return AnimatedContainer(
      duration: animationDuration,
      padding: effectiveStyle.padding,
      decoration: effectiveStyle.decoration,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            effectiveStyle.icon,
            color: effectiveStyle.iconColor,
            size: effectiveStyle.iconSize,
          ),
          SizedBox(width: effectiveStyle.spacing),
          Text(
            '${progress.currentCycle}. Döngü',
            style: effectiveStyle.textStyle,
          ),
        ],
      ),
    );
  }
}

/// Döngü göstergesi stil sınıfı
class CycleIndicatorStyle {
  /// Padding
  final EdgeInsetsGeometry padding;
  
  /// Dekorasyon
  final BoxDecoration decoration;
  
  /// Icon
  final IconData icon;
  
  /// Icon rengi
  final Color iconColor;
  
  /// Icon boyutu
  final double iconSize;
  
  /// Text stili
  final TextStyle textStyle;
  
  /// Icon ile text arası boşluk
  final double spacing;

  const CycleIndicatorStyle({
    required this.padding,
    required this.decoration,
    required this.icon,
    required this.iconColor,
    required this.iconSize,
    required this.textStyle,
    this.spacing = 6.0,
  });

  /// Varsayılan stil
  factory CycleIndicatorStyle.defaultStyle(ThemeData theme) {
    return CycleIndicatorStyle(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      icon: Icons.repeat,
      iconColor: Colors.white,
      iconSize: 16,
      textStyle: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: 14,
      ),
    );
  }

  /// Başarı stili (yeşil)
  factory CycleIndicatorStyle.successStyle(ThemeData theme) {
    return CycleIndicatorStyle(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.green,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      icon: Icons.check_circle,
      iconColor: Colors.white,
      iconSize: 16,
      textStyle: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: 14,
      ),
    );
  }

  /// Minimal stil
  factory CycleIndicatorStyle.minimalStyle(ThemeData theme) {
    return CycleIndicatorStyle(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      icon: Icons.repeat,
      iconColor: theme.colorScheme.onPrimaryContainer,
      iconSize: 14,
      textStyle: TextStyle(
        color: theme.colorScheme.onPrimaryContainer,
        fontWeight: FontWeight.w600,
        fontSize: 12,
      ),
    );
  }

  /// Büyük stil
  factory CycleIndicatorStyle.largeStyle(ThemeData theme) {
    return CycleIndicatorStyle(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.4),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      icon: Icons.repeat,
      iconColor: Colors.white,
      iconSize: 20,
      textStyle: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: 16,
      ),
      spacing: 8.0,
    );
  }
}

/// Animasyonlu döngü göstergesi
class AnimatedCycleIndicator extends ConsumerStatefulWidget {
  /// Program ID'si
  final int programId;
  
  /// Özel stil
  final CycleIndicatorStyle? style;

  const AnimatedCycleIndicator({
    super.key,
    required this.programId,
    this.style,
  });

  @override
  ConsumerState<AnimatedCycleIndicator> createState() => _AnimatedCycleIndicatorState();
}

class _AnimatedCycleIndicatorState extends ConsumerState<AnimatedCycleIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  
  int? _previousCycle;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progress = ref.watch(programProgressProvider(widget.programId));
    
    // İlerleme verisi yoksa veya 1. döngüdeyse gösterme
    if (progress == null || progress.currentCycle <= 1) {
      return const SizedBox.shrink();
    }

    // Döngü değişti mi kontrol et
    if (_previousCycle != progress.currentCycle) {
      _previousCycle = progress.currentCycle;
      _animationController.reset();
      _animationController.forward();
    }

    final effectiveStyle = widget.style ?? CycleIndicatorStyle.defaultStyle(theme);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              padding: effectiveStyle.padding,
              decoration: effectiveStyle.decoration,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    effectiveStyle.icon,
                    color: effectiveStyle.iconColor,
                    size: effectiveStyle.iconSize,
                  ),
                  SizedBox(width: effectiveStyle.spacing),
                  Text(
                    '${progress.currentCycle}. Döngü',
                    style: effectiveStyle.textStyle,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Responsive döngü göstergesi
class ResponsiveCycleIndicator extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Animasyonlu mu?
  final bool animated;

  const ResponsiveCycleIndicator({
    super.key,
    required this.programId,
    this.animated = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final theme = Theme.of(context);
        
        // Ekran boyutuna göre stil seç
        CycleIndicatorStyle style;
        if (constraints.maxWidth > 600) {
          style = CycleIndicatorStyle.largeStyle(theme);
        } else if (constraints.maxWidth > 400) {
          style = CycleIndicatorStyle.defaultStyle(theme);
        } else {
          style = CycleIndicatorStyle.minimalStyle(theme);
        }

        if (animated) {
          return AnimatedCycleIndicator(
            programId: programId,
            style: style,
          );
        } else {
          return CycleIndicator(
            programId: programId,
            style: style,
          );
        }
      },
    );
  }
}
