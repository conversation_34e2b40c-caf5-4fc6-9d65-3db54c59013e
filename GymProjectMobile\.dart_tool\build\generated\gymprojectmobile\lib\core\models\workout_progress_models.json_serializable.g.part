// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProgramProgressModel _$ProgramProgressModelFromJson(
  Map<String, dynamic> json,
) => ProgramProgressModel(
  programId: (json['programId'] as num).toInt(),
  dayCompletions:
      (json['dayCompletions'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(int.parse(k), e as bool),
      ) ??
      const {},
  completionDates:
      (json['completionDates'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
          int.parse(k),
          e == null ? null : DateTime.parse(e as String),
        ),
      ) ??
      const {},
  currentCycle: (json['currentCycle'] as num?)?.toInt() ?? 1,
  lastActivityDate:
      json['lastActivityDate'] == null
          ? null
          : DateTime.parse(json['lastActivityDate'] as String),
  totalCompletions: (json['totalCompletions'] as num?)?.toInt() ?? 0,
  lastResetDate:
      json['lastResetDate'] == null
          ? null
          : DateTime.parse(json['lastResetDate'] as String),
);

Map<String, dynamic> _$ProgramProgressModelToJson(
  ProgramProgressModel instance,
) => <String, dynamic>{
  'programId': instance.programId,
  'dayCompletions': instance.dayCompletions.map(
    (k, e) => MapEntry(k.toString(), e),
  ),
  'completionDates': instance.completionDates.map(
    (k, e) => MapEntry(k.toString(), e?.toIso8601String()),
  ),
  'currentCycle': instance.currentCycle,
  'lastActivityDate': instance.lastActivityDate?.toIso8601String(),
  'totalCompletions': instance.totalCompletions,
  'lastResetDate': instance.lastResetDate?.toIso8601String(),
};

DayProgressModel _$DayProgressModelFromJson(Map<String, dynamic> json) =>
    DayProgressModel(
      dayId: (json['dayId'] as num).toInt(),
      dayNumber: (json['dayNumber'] as num).toInt(),
      dayName: json['dayName'] as String,
      isRestDay: json['isRestDay'] as bool,
      isCompleted: json['isCompleted'] as bool? ?? false,
      completedDate:
          json['completedDate'] == null
              ? null
              : DateTime.parse(json['completedDate'] as String),
    );

Map<String, dynamic> _$DayProgressModelToJson(DayProgressModel instance) =>
    <String, dynamic>{
      'dayId': instance.dayId,
      'dayNumber': instance.dayNumber,
      'dayName': instance.dayName,
      'isRestDay': instance.isRestDay,
      'isCompleted': instance.isCompleted,
      'completedDate': instance.completedDate?.toIso8601String(),
    };

ProgressStatsModel _$ProgressStatsModelFromJson(Map<String, dynamic> json) =>
    ProgressStatsModel(
      programId: (json['programId'] as num).toInt(),
      totalCycles: (json['totalCycles'] as num?)?.toInt() ?? 1,
      totalCompletedDays: (json['totalCompletedDays'] as num?)?.toInt() ?? 0,
      longestStreak: (json['longestStreak'] as num?)?.toInt() ?? 0,
      currentStreak: (json['currentStreak'] as num?)?.toInt() ?? 0,
      firstStartDate:
          json['firstStartDate'] == null
              ? null
              : DateTime.parse(json['firstStartDate'] as String),
      lastActivityDate:
          json['lastActivityDate'] == null
              ? null
              : DateTime.parse(json['lastActivityDate'] as String),
    );

Map<String, dynamic> _$ProgressStatsModelToJson(ProgressStatsModel instance) =>
    <String, dynamic>{
      'programId': instance.programId,
      'totalCycles': instance.totalCycles,
      'totalCompletedDays': instance.totalCompletedDays,
      'longestStreak': instance.longestStreak,
      'currentStreak': instance.currentStreak,
      'firstStartDate': instance.firstStartDate?.toIso8601String(),
      'lastActivityDate': instance.lastActivityDate?.toIso8601String(),
    };
