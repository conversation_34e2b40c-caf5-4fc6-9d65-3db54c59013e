/// Local Workout Progress Service - GymKod Pro Mobile
///
/// Bu service antrenman ilerleme verilerini lokal olarak SharedPreferences'da saklar.
/// Backend'e bağımlı olmadan çalışır, hızlı ve offline eri<PERSON>im <PERSON>ğlar.
library;

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

/// Lokal antrenman ilerleme servisi
class LocalWorkoutProgressService {
  // SharedPreferences anahtarları
  static const String _keyPrefix = 'workout_progress_';
  static const String _cyclePrefix = 'workout_cycle_';
  static const String _statsPrefix = 'workout_stats_';
  
  // Spam koruması için cooldown süresi
  static const Duration _cooldownDuration = Duration(seconds: 2);
  static final Map<String, DateTime> _lastClickTimes = {};

  /// Program ilerleme verilerini yükle
  static Future<ProgramProgressModel> loadProgramProgress(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Gün tamamlama durumları
    final dayCompletions = <int, bool>{};
    final completionDates = <int, DateTime?>{};
    
    // Tüm anahtarları tara ve bu programa ait olanları bul
    final keys = prefs.getKeys();
    final programKeys = keys.where((key) => key.startsWith('$_keyPrefix${programId}_'));
    
    for (final key in programKeys) {
      if (key.endsWith('_completed')) {
        // Gün ID'sini çıkar: workout_progress_123_5_completed -> 5
        final parts = key.split('_');
        if (parts.length >= 4) {
          final dayId = int.tryParse(parts[3]);
          if (dayId != null) {
            dayCompletions[dayId] = prefs.getBool(key) ?? false;
          }
        }
      } else if (key.endsWith('_date')) {
        // Tamamlanma tarihini çıkar
        final parts = key.split('_');
        if (parts.length >= 4) {
          final dayId = int.tryParse(parts[3]);
          if (dayId != null) {
            final dateStr = prefs.getString(key);
            completionDates[dayId] = dateStr != null ? DateTime.tryParse(dateStr) : null;
          }
        }
      }
    }
    
    // Döngü bilgileri
    final currentCycle = prefs.getInt('$_cyclePrefix${programId}_current') ?? 1;

    // İstatistikler
    final totalCompletions = prefs.getInt('$_statsPrefix${programId}_total_completions') ?? 0;
    final lastActivityStr = prefs.getString('$_statsPrefix${programId}_last_activity');
    final lastResetStr = prefs.getString('$_statsPrefix${programId}_last_reset');
    
    return ProgramProgressModel(
      programId: programId,
      dayCompletions: dayCompletions,
      completionDates: completionDates,
      currentCycle: currentCycle,
      totalCompletions: totalCompletions,
      lastActivityDate: lastActivityStr != null ? DateTime.tryParse(lastActivityStr) : null,
      lastResetDate: lastResetStr != null ? DateTime.tryParse(lastResetStr) : null,
    );
  }

  /// Gün tamamlama durumunu değiştir (toggle)
  static Future<bool> toggleDayCompletion(int programId, int dayId) async {
    // Spam koruması kontrolü
    if (!_canToggleDay(programId, dayId)) {
      return false;
    }
    
    _recordClick(programId, dayId);
    
    final prefs = await SharedPreferences.getInstance();
    final completedKey = '$_keyPrefix${programId}_${dayId}_completed';
    final dateKey = '$_keyPrefix${programId}_${dayId}_date';
    
    // Mevcut durumu al
    final currentStatus = prefs.getBool(completedKey) ?? false;
    final newStatus = !currentStatus;
    
    // Yeni durumu kaydet
    await prefs.setBool(completedKey, newStatus);
    
    if (newStatus) {
      // Tamamlandıysa tarihi kaydet
      await prefs.setString(dateKey, DateTime.now().toIso8601String());
      
      // Toplam tamamlama sayısını artır
      await _incrementTotalCompletions(programId);
    } else {
      // Tamamlama iptal edildiyse tarihi sil
      await prefs.remove(dateKey);
      
      // Toplam tamamlama sayısını azalt
      await _decrementTotalCompletions(programId);
    }
    
    // Son aktivite tarihini güncelle
    await _updateLastActivity(programId);
    
    return newStatus;
  }

  /// Programı sıfırla (tüm ilerlemeyi temizle)
  static Future<void> resetProgramProgress(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Bu programa ait tüm anahtarları bul ve sil
    final keys = prefs.getKeys();
    final programKeys = keys.where((key) =>
      key.startsWith('$_keyPrefix${programId}_') ||
      key.startsWith('$_cyclePrefix${programId}_')
    );

    for (final key in programKeys) {
      await prefs.remove(key);
    }

    // Döngüyü 1'e sıfırla
    await prefs.setInt('$_cyclePrefix${programId}_current', 1);

    // Sıfırlama tarihini kaydet
    await prefs.setString('$_statsPrefix${programId}_last_reset',
                         DateTime.now().toIso8601String());

    // Toplam tamamlama sayısını sıfırla
    await prefs.setInt('$_statsPrefix${programId}_total_completions', 0);
    
    // Son aktivite tarihini güncelle
    await _updateLastActivity(programId);
  }

  /// Yeni döngü başlat
  static Future<int> startNewCycle(int programId, List<int> workoutDayIds) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Mevcut döngü sayısını al ve artır
    final currentCycle = prefs.getInt('$_cyclePrefix${programId}_current') ?? 1;
    final newCycle = currentCycle + 1;

    // Yeni döngü sayısını kaydet
    await prefs.setInt('$_cyclePrefix${programId}_current', newCycle);

    // Döngü başlangıç tarihini kaydet
    await prefs.setString('$_cyclePrefix${programId}_${newCycle}_start_date',
                         DateTime.now().toIso8601String());

    // Tüm günlerin tamamlama durumunu sıfırla
    for (final dayId in workoutDayIds) {
      await prefs.remove('$_keyPrefix${programId}_${dayId}_completed');
      await prefs.remove('$_keyPrefix${programId}_${dayId}_date');
    }
    
    // Son aktivite tarihini güncelle
    await _updateLastActivity(programId);
    
    return newCycle;
  }

  /// Tüm antrenman günlerinin tamamlanıp tamamlanmadığını kontrol et
  static Future<bool> checkAllWorkoutDaysCompleted(int programId, List<int> workoutDayIds) async {
    if (workoutDayIds.isEmpty) return false;
    
    final prefs = await SharedPreferences.getInstance();
    
    for (final dayId in workoutDayIds) {
      final isCompleted = prefs.getBool('$_keyPrefix${programId}_${dayId}_completed') ?? false;
      if (!isCompleted) {
        return false;
      }
    }
    
    return true;
  }

  /// Program istatistiklerini al
  static Future<ProgressStatsModel> getProgressStats(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    
    final totalCycles = prefs.getInt('$_cyclePrefix${programId}_current') ?? 1;
    final totalCompletions = prefs.getInt('$_statsPrefix${programId}_total_completions') ?? 0;
    final lastActivityStr = prefs.getString('$_statsPrefix${programId}_last_activity');
    final firstStartStr = prefs.getString('$_statsPrefix${programId}_first_start');
    
    return ProgressStatsModel(
      programId: programId,
      totalCycles: totalCycles,
      totalCompletedDays: totalCompletions,
      lastActivityDate: lastActivityStr != null ? DateTime.tryParse(lastActivityStr) : null,
      firstStartDate: firstStartStr != null ? DateTime.tryParse(firstStartStr) : null,
    );
  }

  /// Tüm program verilerini temizle (uygulama sıfırlama için)
  static Future<void> clearAllProgressData() async {
    final prefs = await SharedPreferences.getInstance();
    
    final keys = prefs.getKeys();
    final progressKeys = keys.where((key) => 
      key.startsWith(_keyPrefix) ||
      key.startsWith(_cyclePrefix) ||
      key.startsWith(_statsPrefix)
    );
    
    for (final key in progressKeys) {
      await prefs.remove(key);
    }
  }

  // Private helper methods

  /// Spam koruması - belirli süre içinde aynı güne tıklamayı engelle
  static bool _canToggleDay(int programId, int dayId) {
    final key = '${programId}_${dayId}';
    final lastClick = _lastClickTimes[key];
    
    if (lastClick == null) return true;
    
    final timeSinceLastClick = DateTime.now().difference(lastClick);
    return timeSinceLastClick >= _cooldownDuration;
  }

  /// Tıklama zamanını kaydet
  static void _recordClick(int programId, int dayId) {
    final key = '${programId}_$dayId';
    _lastClickTimes[key] = DateTime.now();
  }

  /// Toplam tamamlama sayısını artır
  static Future<void> _incrementTotalCompletions(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    final key = '$_statsPrefix${programId}_total_completions';
    final current = prefs.getInt(key) ?? 0;
    await prefs.setInt(key, current + 1);
  }

  /// Toplam tamamlama sayısını azalt
  static Future<void> _decrementTotalCompletions(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    final key = '${_statsPrefix}${programId}_total_completions';
    final current = prefs.getInt(key) ?? 0;
    if (current > 0) {
      await prefs.setInt(key, current - 1);
    }
  }

  /// Son aktivite tarihini güncelle
  static Future<void> _updateLastActivity(int programId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('${_statsPrefix}${programId}_last_activity', 
                         DateTime.now().toIso8601String());
    
    // İlk başlangıç tarihini de kaydet (eğer yoksa)
    final firstStartKey = '${_statsPrefix}${programId}_first_start';
    if (!prefs.containsKey(firstStartKey)) {
      await prefs.setString(firstStartKey, DateTime.now().toIso8601String());
    }
  }
}
