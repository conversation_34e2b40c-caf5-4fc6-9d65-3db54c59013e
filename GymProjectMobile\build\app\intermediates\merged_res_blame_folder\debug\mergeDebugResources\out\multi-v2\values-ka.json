{"logs": [{"outputFile": "com.example.gymprojectmobile.app-mergeDebugResources-40:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\89bb887b47984212ea06b6247b94e176\\transformed\\appcompat-1.1.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,894,985,1077,1172,1266,1367,1460,1555,1650,1741,1832,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,79,90,91,94,93,100,92,94,94,90,90,79,112,105,97,112,104,103,157,98,80", "endOffsets": "208,311,422,508,613,726,809,889,980,1072,1167,1261,1362,1455,1550,1645,1736,1827,1907,2020,2126,2224,2337,2442,2546,2704,2803,2884"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,894,985,1077,1172,1266,1367,1460,1555,1650,1741,1832,1912,2025,2131,2229,2342,2447,2551,2709,3917", "endColumns": "107,102,110,85,104,112,82,79,90,91,94,93,100,92,94,94,90,90,79,112,105,97,112,104,103,157,98,80", "endOffsets": "208,311,422,508,613,726,809,889,980,1072,1167,1261,1362,1455,1550,1645,1736,1827,1907,2020,2126,2224,2337,2442,2546,2704,2803,3993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8fab08a6f6e27ebe9881e5845ef81bc7\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2808,2904,3006,3105,3204,3310,3414,3998", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "2899,3001,3100,3199,3305,3409,3527,4094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00e4bc24da987e892a8683a085ee5f6a\\transformed\\preference-1.2.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,350,490,659,745", "endColumns": "71,87,84,139,168,85,80", "endOffsets": "172,260,345,485,654,740,821"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3532,3604,3692,3777,4099,4268,4354", "endColumns": "71,87,84,139,168,85,80", "endOffsets": "3599,3687,3772,3912,4263,4349,4430"}}]}]}