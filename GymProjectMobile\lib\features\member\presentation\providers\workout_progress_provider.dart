/// Workout Progress Provider - GymKod Pro Mobile
///
/// Bu provider antrenman ilerleme takibi için state management sağlar.
/// Lokal SharedPreferences kullanarak offline çalışır.
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';

/// Workout Progress State
class WorkoutProgressState {
  /// Program ID'sine göre ilerleme verileri
  final Map<int, ProgramProgressModel> progresses;
  
  /// Loading durumundaki program ID'leri
  final Set<int> loadingPrograms;
  
  /// Gün toggle işlemi yapılan program-gün çiftleri
  final Set<String> loadingDays;
  
  /// Hata mesajı
  final String? error;
  
  /// Başarı mesajı
  final String? successMessage;
  
  /// Son güncelleme tarihi
  final DateTime? lastUpdated;

  const WorkoutProgressState({
    this.progresses = const {},
    this.loadingPrograms = const {},
    this.loadingDays = const {},
    this.error,
    this.successMessage,
    this.lastUpdated,
  });

  WorkoutProgressState copyWith({
    Map<int, ProgramProgressModel>? progresses,
    Set<int>? loadingPrograms,
    Set<String>? loadingDays,
    String? error,
    String? successMessage,
    DateTime? lastUpdated,
    bool clearError = false,
    bool clearSuccessMessage = false,
  }) {
    return WorkoutProgressState(
      progresses: progresses ?? this.progresses,
      loadingPrograms: loadingPrograms ?? this.loadingPrograms,
      loadingDays: loadingDays ?? this.loadingDays,
      error: clearError ? null : (error ?? this.error),
      successMessage: clearSuccessMessage ? null : (successMessage ?? this.successMessage),
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Belirli bir programın ilerleme verilerini al
  ProgramProgressModel? getProgress(int programId) {
    return progresses[programId];
  }

  /// Belirli bir programın yüklenip yüklenmediğini kontrol et
  bool isProgramLoading(int programId) {
    return loadingPrograms.contains(programId);
  }

  /// Belirli bir günün toggle işleminde olup olmadığını kontrol et
  bool isDayLoading(int programId, int dayId) {
    return loadingDays.contains('${programId}_${dayId}');
  }

  /// Belirli bir günün tamamlanıp tamamlanmadığını kontrol et
  bool isDayCompleted(int programId, int dayId) {
    final progress = progresses[programId];
    return progress?.isDayCompleted(dayId) ?? false;
  }

  @override
  String toString() {
    return 'WorkoutProgressState(progresses: ${progresses.length}, loadingPrograms: ${loadingPrograms.length}, error: $error)';
  }
}

/// Workout Progress Notifier
class WorkoutProgressNotifier extends StateNotifier<WorkoutProgressState> {
  WorkoutProgressNotifier() : super(const WorkoutProgressState());

  /// Program ilerleme verilerini yükle
  Future<void> loadProgramProgress(int programId) async {
    try {
      LoggingService.stateLog('WorkoutProgress', 'Loading progress for program $programId');

      // Loading state'i ekle
      state = state.copyWith(
        loadingPrograms: {...state.loadingPrograms, programId},
        clearError: true,
        clearSuccessMessage: true,
      );

      // Lokal servisten verileri yükle
      final progress = await LocalWorkoutProgressService.loadProgramProgress(programId);

      LoggingService.stateLog(
        'WorkoutProgress',
        'Progress loaded for program $programId',
        state: 'Cycle: ${progress.currentCycle}, Completed: ${progress.getCompletedDaysInCurrentCycle()}',
      );

      // State'i güncelle
      final newProgresses = Map<int, ProgramProgressModel>.from(state.progresses);
      newProgresses[programId] = progress;

      final newLoadingPrograms = Set<int>.from(state.loadingPrograms);
      newLoadingPrograms.remove(programId);

      state = state.copyWith(
        progresses: newProgresses,
        loadingPrograms: newLoadingPrograms,
        lastUpdated: DateTime.now(),
      );
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgressNotifier.loadProgramProgress',
      );

      // Loading state'i kaldır
      final newLoadingPrograms = Set<int>.from(state.loadingPrograms);
      newLoadingPrograms.remove(programId);

      state = state.copyWith(
        loadingPrograms: newLoadingPrograms,
        error: 'İlerleme verileri yüklenirken hata oluştu',
      );
    }
  }

  /// Gün tamamlama durumunu değiştir
  Future<void> toggleDayCompletion(
    int programId,
    int dayId,
    List<int> allWorkoutDayIds,
  ) async {
    final dayKey = '${programId}_${dayId}';

    try {
      LoggingService.stateLog('WorkoutProgress', 'Toggling day $dayId for program $programId');

      // Loading state'i ekle
      state = state.copyWith(
        loadingDays: {...state.loadingDays, dayKey},
        clearError: true,
        clearSuccessMessage: true,
      );

      // Lokal servisten toggle işlemi yap
      final newStatus = await LocalWorkoutProgressService.toggleDayCompletion(programId, dayId);

      if (!newStatus) {
        // Spam koruması devreye girdi
        LoggingService.stateLog('WorkoutProgress', 'Toggle blocked by spam protection');
        
        final newLoadingDays = Set<String>.from(state.loadingDays);
        newLoadingDays.remove(dayKey);
        
        state = state.copyWith(
          loadingDays: newLoadingDays,
          error: 'Çok hızlı tıklıyorsunuz, lütfen bekleyin',
        );
        return;
      }

      // İlerleme verilerini yeniden yükle
      final updatedProgress = await LocalWorkoutProgressService.loadProgramProgress(programId);

      // Tüm günler tamamlandı mı kontrol et
      final allCompleted = await LocalWorkoutProgressService.checkAllWorkoutDaysCompleted(
        programId,
        allWorkoutDayIds,
      );

      String? successMessage;
      if (allCompleted) {
        // Yeni döngü başlat
        final newCycle = await LocalWorkoutProgressService.startNewCycle(programId, allWorkoutDayIds);
        successMessage = 'Tebrikler! ${updatedProgress.currentCycle}. döngüyü tamamladınız. ${newCycle}. döngü başlıyor! 🎉';
        
        // İlerleme verilerini tekrar yükle (döngü sıfırlandığı için)
        final finalProgress = await LocalWorkoutProgressService.loadProgramProgress(programId);
        
        // State'i güncelle
        final newProgresses = Map<int, ProgramProgressModel>.from(state.progresses);
        newProgresses[programId] = finalProgress;

        final newLoadingDays = Set<String>.from(state.loadingDays);
        newLoadingDays.remove(dayKey);

        state = state.copyWith(
          progresses: newProgresses,
          loadingDays: newLoadingDays,
          successMessage: successMessage,
          lastUpdated: DateTime.now(),
        );
      } else {
        // Normal güncelleme
        final newProgresses = Map<int, ProgramProgressModel>.from(state.progresses);
        newProgresses[programId] = updatedProgress;

        final newLoadingDays = Set<String>.from(state.loadingDays);
        newLoadingDays.remove(dayKey);

        state = state.copyWith(
          progresses: newProgresses,
          loadingDays: newLoadingDays,
          lastUpdated: DateTime.now(),
        );
      }

      LoggingService.stateLog(
        'WorkoutProgress',
        'Day toggle completed',
        state: 'New status: $newStatus, All completed: $allCompleted',
      );
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgressNotifier.toggleDayCompletion',
      );

      // Loading state'i kaldır
      final newLoadingDays = Set<String>.from(state.loadingDays);
      newLoadingDays.remove(dayKey);

      state = state.copyWith(
        loadingDays: newLoadingDays,
        error: 'Gün tamamlama işlemi sırasında hata oluştu',
      );
    }
  }

  /// Programı sıfırla
  Future<void> resetProgramProgress(int programId) async {
    try {
      LoggingService.stateLog('WorkoutProgress', 'Resetting progress for program $programId');

      // Loading state'i ekle
      state = state.copyWith(
        loadingPrograms: {...state.loadingPrograms, programId},
        clearError: true,
        clearSuccessMessage: true,
      );

      // Lokal servisten sıfırlama işlemi yap
      await LocalWorkoutProgressService.resetProgramProgress(programId);

      // İlerleme verilerini yeniden yükle
      final resetProgress = await LocalWorkoutProgressService.loadProgramProgress(programId);

      // State'i güncelle
      final newProgresses = Map<int, ProgramProgressModel>.from(state.progresses);
      newProgresses[programId] = resetProgress;

      final newLoadingPrograms = Set<int>.from(state.loadingPrograms);
      newLoadingPrograms.remove(programId);

      state = state.copyWith(
        progresses: newProgresses,
        loadingPrograms: newLoadingPrograms,
        successMessage: 'Program başarıyla sıfırlandı! 🔄',
        lastUpdated: DateTime.now(),
      );

      LoggingService.stateLog('WorkoutProgress', 'Program reset completed');
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgressNotifier.resetProgramProgress',
      );

      // Loading state'i kaldır
      final newLoadingPrograms = Set<int>.from(state.loadingPrograms);
      newLoadingPrograms.remove(programId);

      state = state.copyWith(
        loadingPrograms: newLoadingPrograms,
        error: 'Program sıfırlama işlemi sırasında hata oluştu',
      );
    }
  }

  /// Hata mesajını temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Başarı mesajını temizle
  void clearSuccessMessage() {
    state = state.copyWith(clearSuccessMessage: true);
  }

  /// State'i sıfırla
  void reset() {
    LoggingService.stateLog('WorkoutProgress', 'Resetting state');
    state = const WorkoutProgressState();
  }

  /// Tüm ilerleme verilerini temizle
  Future<void> clearAllProgressData() async {
    try {
      LoggingService.stateLog('WorkoutProgress', 'Clearing all progress data');
      
      await LocalWorkoutProgressService.clearAllProgressData();
      
      state = const WorkoutProgressState();
      
      LoggingService.stateLog('WorkoutProgress', 'All progress data cleared');
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgressNotifier.clearAllProgressData',
      );
      
      state = state.copyWith(
        error: 'Tüm veriler temizlenirken hata oluştu',
      );
    }
  }
}

/// Workout Progress Provider
final workoutProgressProvider = StateNotifierProvider<WorkoutProgressNotifier, WorkoutProgressState>((ref) {
  return WorkoutProgressNotifier();
});

/// Workout Progress State Getters
final workoutProgressLoadingProvider = Provider.family<bool, int>((ref, programId) {
  return ref.watch(workoutProgressProvider).isProgramLoading(programId);
});

final dayCompletionLoadingProvider = Provider.family<bool, String>((ref, dayKey) {
  final parts = dayKey.split('_');
  if (parts.length != 2) return false;
  
  final programId = int.tryParse(parts[0]);
  final dayId = int.tryParse(parts[1]);
  
  if (programId == null || dayId == null) return false;
  
  return ref.watch(workoutProgressProvider).isDayLoading(programId, dayId);
});

final dayCompletionProvider = Provider.family<bool, String>((ref, dayKey) {
  final parts = dayKey.split('_');
  if (parts.length != 2) return false;
  
  final programId = int.tryParse(parts[0]);
  final dayId = int.tryParse(parts[1]);
  
  if (programId == null || dayId == null) return false;
  
  return ref.watch(workoutProgressProvider).isDayCompleted(programId, dayId);
});

final programProgressProvider = Provider.family<ProgramProgressModel?, int>((ref, programId) {
  return ref.watch(workoutProgressProvider).getProgress(programId);
});

final workoutProgressErrorProvider = Provider<String?>((ref) {
  return ref.watch(workoutProgressProvider).error;
});

final workoutProgressSuccessProvider = Provider<String?>((ref) {
  return ref.watch(workoutProgressProvider).successMessage;
});
