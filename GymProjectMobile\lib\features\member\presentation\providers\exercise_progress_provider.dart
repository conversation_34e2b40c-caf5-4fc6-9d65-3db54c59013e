/// Exercise Progress Provider - GymKod Pro Mobile
///
/// Bu provider egzersiz bazlı ilerleme takibi için state management sağlar.
/// Gerçekçi antrenman deneyimi için egzersiz tamamlama sistemi.
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';

/// Exercise Progress State
class ExerciseProgressState {
  /// Program ID'sine göre egzersiz durumları (ProgramID -> DayID -> ExerciseID -> bool)
  final Map<int, Map<int, Map<int, bool>>> exerciseStates;
  
  /// Antrenman seansı durumları (ProgramID -> DayID -> bool)
  final Map<int, Map<int, bool>> workoutSessionStates;
  
  /// Loading durumundaki egzersizler (programId_dayId_exerciseId)
  final Set<String> loadingExercises;
  
  /// Loading durumundaki antrenman seansları (programId_dayId)
  final Set<String> loadingSessions;
  
  /// Hata mesajı
  final String? error;
  
  /// Başarı mesajı
  final String? successMessage;
  
  /// Son güncelleme tarihi
  final DateTime? lastUpdated;

  const ExerciseProgressState({
    this.exerciseStates = const {},
    this.workoutSessionStates = const {},
    this.loadingExercises = const {},
    this.loadingSessions = const {},
    this.error,
    this.successMessage,
    this.lastUpdated,
  });

  ExerciseProgressState copyWith({
    Map<int, Map<int, Map<int, bool>>>? exerciseStates,
    Map<int, Map<int, bool>>? workoutSessionStates,
    Set<String>? loadingExercises,
    Set<String>? loadingSessions,
    String? error,
    String? successMessage,
    DateTime? lastUpdated,
    bool clearError = false,
    bool clearSuccessMessage = false,
  }) {
    return ExerciseProgressState(
      exerciseStates: exerciseStates ?? this.exerciseStates,
      workoutSessionStates: workoutSessionStates ?? this.workoutSessionStates,
      loadingExercises: loadingExercises ?? this.loadingExercises,
      loadingSessions: loadingSessions ?? this.loadingSessions,
      error: clearError ? null : (error ?? this.error),
      successMessage: clearSuccessMessage ? null : (successMessage ?? this.successMessage),
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Belirli bir egzersizin tamamlanıp tamamlanmadığını kontrol et
  bool isExerciseCompleted(int programId, int dayId, int exerciseId) {
    return exerciseStates[programId]?[dayId]?[exerciseId] ?? false;
  }

  /// Belirli bir egzersizin loading durumunu kontrol et
  bool isExerciseLoading(int programId, int dayId, int exerciseId) {
    return loadingExercises.contains('${programId}_${dayId}_$exerciseId');
  }

  /// Belirli bir günün tüm egzersizlerinin tamamlanıp tamamlanmadığını kontrol et
  bool isDayCompleted(int programId, int dayId, List<int> exerciseIds) {
    if (exerciseIds.isEmpty) return false;
    
    final dayExercises = exerciseStates[programId]?[dayId];
    if (dayExercises == null) return false;
    
    return exerciseIds.every((exerciseId) => dayExercises[exerciseId] ?? false);
  }

  /// Belirli bir antrenman seansının tamamlanıp tamamlanmadığını kontrol et
  bool isWorkoutSessionCompleted(int programId, int dayId) {
    return workoutSessionStates[programId]?[dayId] ?? false;
  }

  /// Belirli bir antrenman seansının loading durumunu kontrol et
  bool isWorkoutSessionLoading(int programId, int dayId) {
    return loadingSessions.contains('${programId}_$dayId');
  }

  @override
  String toString() {
    return 'ExerciseProgressState(exerciseStates: ${exerciseStates.length}, error: $error)';
  }
}

/// Exercise Progress Notifier
class ExerciseProgressNotifier extends StateNotifier<ExerciseProgressState> {
  ExerciseProgressNotifier() : super(const ExerciseProgressState());

  /// Belirli bir günün egzersiz durumlarını yükle
  Future<void> loadDayExercises(
    int programId, 
    int dayId, 
    List<int> exerciseIds
  ) async {
    try {
      LoggingService.stateLog('ExerciseProgress', 'Loading exercises for day $dayId');

      // Egzersiz durumlarını yükle
      final exercisesStatus = await LocalExerciseProgressService.getDayExercisesStatus(
        programId, dayId, exerciseIds
      );

      // Antrenman seansı durumunu yükle
      final sessionCompleted = await LocalExerciseProgressService.isWorkoutSessionCompleted(
        programId, dayId
      );

      // State'i güncelle
      final newExerciseStates = Map<int, Map<int, Map<int, bool>>>.from(state.exerciseStates);
      if (!newExerciseStates.containsKey(programId)) {
        newExerciseStates[programId] = {};
      }
      newExerciseStates[programId]![dayId] = exercisesStatus;

      final newSessionStates = Map<int, Map<int, bool>>.from(state.workoutSessionStates);
      if (!newSessionStates.containsKey(programId)) {
        newSessionStates[programId] = {};
      }
      newSessionStates[programId]![dayId] = sessionCompleted;

      state = state.copyWith(
        exerciseStates: newExerciseStates,
        workoutSessionStates: newSessionStates,
        lastUpdated: DateTime.now(),
        clearError: true,
      );

      LoggingService.stateLog(
        'ExerciseProgress',
        'Exercises loaded for day $dayId',
        state: 'Completed: ${exercisesStatus.values.where((c) => c).length}/${exercisesStatus.length}',
      );
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.loadDayExercises',
      );

      state = state.copyWith(
        error: 'Egzersiz verileri yüklenirken hata oluştu',
      );
    }
  }

  /// Egzersiz tamamlama durumunu değiştir
  Future<void> toggleExerciseCompletion(
    int programId,
    int dayId,
    int exerciseId,
    List<int> allExerciseIds,
  ) async {
    final exerciseKey = '${programId}_${dayId}_$exerciseId';

    try {
      LoggingService.stateLog('ExerciseProgress', 'Toggling exercise $exerciseId');

      // Loading state'i ekle
      state = state.copyWith(
        loadingExercises: {...state.loadingExercises, exerciseKey},
        clearError: true,
        clearSuccessMessage: true,
      );

      // Lokal servisten toggle işlemi yap
      final newStatus = await LocalExerciseProgressService.toggleExerciseCompletion(
        programId, dayId, exerciseId
      );

      // State'i güncelle
      final newExerciseStates = Map<int, Map<int, Map<int, bool>>>.from(state.exerciseStates);
      if (!newExerciseStates.containsKey(programId)) {
        newExerciseStates[programId] = {};
      }
      if (!newExerciseStates[programId]!.containsKey(dayId)) {
        newExerciseStates[programId]![dayId] = {};
      }
      newExerciseStates[programId]![dayId]![exerciseId] = newStatus;

      final newLoadingExercises = Set<String>.from(state.loadingExercises);
      newLoadingExercises.remove(exerciseKey);

      // Tüm egzersizler tamamlandı mı kontrol et
      final allCompleted = allExerciseIds.every((id) =>
        newExerciseStates[programId]![dayId]![id] ?? false
      );

      // Eğer antrenman seansı tamamlanmışsa ve artık tüm egzersizler tamamlanmadıysa, seansı iptal et
      final isSessionCompleted = state.isWorkoutSessionCompleted(programId, dayId);

      if (isSessionCompleted && !allCompleted) {
        // Antrenman seansını iptal et
        await LocalExerciseProgressService.cancelWorkoutSession(programId, dayId);

        // Session state'i güncelle
        final newSessionStates = Map<int, Map<int, bool>>.from(state.workoutSessionStates);
        if (newSessionStates.containsKey(programId)) {
          newSessionStates[programId]![dayId] = false;
        }

        state = state.copyWith(
          workoutSessionStates: newSessionStates,
        );

        LoggingService.stateLog('ExerciseProgress', 'Workout session cancelled due to incomplete exercises');
      }

      String? successMessage;
      if (allCompleted && !isSessionCompleted) {
        successMessage = 'Tebrikler! Tüm egzersizleri tamamladınız. "Antrenmanı Bitir" butonuna basabilirsiniz! 💪';
      }

      state = state.copyWith(
        exerciseStates: newExerciseStates,
        loadingExercises: newLoadingExercises,
        successMessage: successMessage,
        lastUpdated: DateTime.now(),
      );

      LoggingService.stateLog(
        'ExerciseProgress',
        'Exercise toggle completed',
        state: 'New status: $newStatus, All completed: $allCompleted',
      );
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.toggleExerciseCompletion',
      );

      // Loading state'i kaldır
      final newLoadingExercises = Set<String>.from(state.loadingExercises);
      newLoadingExercises.remove(exerciseKey);

      state = state.copyWith(
        loadingExercises: newLoadingExercises,
        error: 'Egzersiz tamamlama işlemi sırasında hata oluştu',
      );
    }
  }

  /// Antrenman seansını tamamla
  Future<void> completeWorkoutSession(int programId, int dayId) async {
    final sessionKey = '${programId}_$dayId';

    try {
      LoggingService.stateLog('ExerciseProgress', 'Completing workout session');

      // Loading state'i ekle
      state = state.copyWith(
        loadingSessions: {...state.loadingSessions, sessionKey},
        clearError: true,
        clearSuccessMessage: true,
      );

      // Lokal servisten antrenman seansını tamamla
      await LocalExerciseProgressService.completeWorkoutSession(programId, dayId);

      // State'i güncelle
      final newSessionStates = Map<int, Map<int, bool>>.from(state.workoutSessionStates);
      if (!newSessionStates.containsKey(programId)) {
        newSessionStates[programId] = {};
      }
      newSessionStates[programId]![dayId] = true;

      final newLoadingSessions = Set<String>.from(state.loadingSessions);
      newLoadingSessions.remove(sessionKey);

      state = state.copyWith(
        workoutSessionStates: newSessionStates,
        loadingSessions: newLoadingSessions,
        successMessage: 'Antrenmanınız başarıyla tamamlandı! 🎉',
        lastUpdated: DateTime.now(),
      );

      LoggingService.stateLog('ExerciseProgress', 'Workout session completed');
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.completeWorkoutSession',
      );

      // Loading state'i kaldır
      final newLoadingSessions = Set<String>.from(state.loadingSessions);
      newLoadingSessions.remove(sessionKey);

      state = state.copyWith(
        loadingSessions: newLoadingSessions,
        error: 'Antrenman tamamlama işlemi sırasında hata oluştu',
      );
    }
  }

  /// Günün egzersizlerini sıfırla
  Future<void> resetDayExercises(
    int programId, 
    int dayId, 
    List<int> exerciseIds
  ) async {
    try {
      LoggingService.stateLog('ExerciseProgress', 'Resetting day exercises');

      await LocalExerciseProgressService.resetDayExercises(programId, dayId, exerciseIds);

      // State'i güncelle
      final newExerciseStates = Map<int, Map<int, Map<int, bool>>>.from(state.exerciseStates);
      if (newExerciseStates.containsKey(programId) && 
          newExerciseStates[programId]!.containsKey(dayId)) {
        for (final exerciseId in exerciseIds) {
          newExerciseStates[programId]![dayId]![exerciseId] = false;
        }
      }

      final newSessionStates = Map<int, Map<int, bool>>.from(state.workoutSessionStates);
      if (newSessionStates.containsKey(programId)) {
        newSessionStates[programId]![dayId] = false;
      }

      state = state.copyWith(
        exerciseStates: newExerciseStates,
        workoutSessionStates: newSessionStates,
        successMessage: 'Gün egzersizleri sıfırlandı! 🔄',
        lastUpdated: DateTime.now(),
      );

      LoggingService.stateLog('ExerciseProgress', 'Day exercises reset completed');
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.resetDayExercises',
      );

      state = state.copyWith(
        error: 'Egzersiz sıfırlama işlemi sırasında hata oluştu',
      );
    }
  }

  /// Hata mesajını temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Başarı mesajını temizle
  void clearSuccessMessage() {
    state = state.copyWith(clearSuccessMessage: true);
  }

  /// State'i sıfırla
  void reset() {
    LoggingService.stateLog('ExerciseProgress', 'Resetting state');
    state = const ExerciseProgressState();
  }
}

/// Exercise Progress Provider
final exerciseProgressProvider = StateNotifierProvider<ExerciseProgressNotifier, ExerciseProgressState>((ref) {
  return ExerciseProgressNotifier();
});

/// Exercise Progress State Getters
final exerciseCompletionProvider = Provider.family<bool, String>((ref, exerciseKey) {
  final parts = exerciseKey.split('_');
  if (parts.length != 3) return false;
  
  final programId = int.tryParse(parts[0]);
  final dayId = int.tryParse(parts[1]);
  final exerciseId = int.tryParse(parts[2]);
  
  if (programId == null || dayId == null || exerciseId == null) return false;
  
  return ref.watch(exerciseProgressProvider).isExerciseCompleted(programId, dayId, exerciseId);
});

final exerciseLoadingProvider = Provider.family<bool, String>((ref, exerciseKey) {
  final parts = exerciseKey.split('_');
  if (parts.length != 3) return false;
  
  final programId = int.tryParse(parts[0]);
  final dayId = int.tryParse(parts[1]);
  final exerciseId = int.tryParse(parts[2]);
  
  if (programId == null || dayId == null || exerciseId == null) return false;
  
  return ref.watch(exerciseProgressProvider).isExerciseLoading(programId, dayId, exerciseId);
});

final workoutSessionCompletionProvider = Provider.family<bool, String>((ref, sessionKey) {
  final parts = sessionKey.split('_');
  if (parts.length != 2) return false;
  
  final programId = int.tryParse(parts[0]);
  final dayId = int.tryParse(parts[1]);
  
  if (programId == null || dayId == null) return false;
  
  return ref.watch(exerciseProgressProvider).isWorkoutSessionCompleted(programId, dayId);
});

final workoutSessionLoadingProvider = Provider.family<bool, String>((ref, sessionKey) {
  final parts = sessionKey.split('_');
  if (parts.length != 2) return false;
  
  final programId = int.tryParse(parts[0]);
  final dayId = int.tryParse(parts[1]);
  
  if (programId == null || dayId == null) return false;
  
  return ref.watch(exerciseProgressProvider).isWorkoutSessionLoading(programId, dayId);
});

final exerciseProgressErrorProvider = Provider<String?>((ref) {
  return ref.watch(exerciseProgressProvider).error;
});

final exerciseProgressSuccessProvider = Provider<String?>((ref) {
  return ref.watch(exerciseProgressProvider).successMessage;
});
