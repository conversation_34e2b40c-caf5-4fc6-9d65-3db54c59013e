/// Exercise Completion Checkbox Widget - GymKod Pro Mobile
///
/// Bu widget egzersizlerin tamamlanma durumunu gösterir ve toggle işlemi yapar.
/// Gerçekçi antrenman deneyimi için egzersiz bazlı takip sistemi.
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/exercise_progress_provider.dart';

/// Egzersiz tamamlama checkbox widget'ı
class ExerciseCompletionCheckbox extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Gün ID'si
  final int dayId;
  
  /// Egzersiz ID'si
  final int exerciseId;
  
  /// Egzersiz adı
  final String exerciseName;
  
  /// Tüm egzersizlerin ID'leri (gün tamamlama kontrolü için)
  final List<int> allExerciseIds;
  
  /// Checkbox boyutu
  final double? size;
  
  /// <PERSON>zel renk
  final Color? activeColor;

  const ExerciseCompletionCheckbox({
    super.key,
    required this.programId,
    required this.dayId,
    required this.exerciseId,
    required this.exerciseName,
    required this.allExerciseIds,
    this.size,
    this.activeColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    // Loading durumunu kontrol et
    final exerciseKey = '${programId}_${dayId}_$exerciseId';
    final isLoading = ref.watch(exerciseLoadingProvider(exerciseKey));
    
    // Tamamlanma durumunu kontrol et
    final isCompleted = ref.watch(exerciseCompletionProvider(exerciseKey));

    return Container(
      width: size ?? 24,
      height: size ?? 24,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isCompleted 
            ? (activeColor ?? Colors.green)
            : theme.colorScheme.outline,
          width: 2,
        ),
        color: isCompleted 
          ? (activeColor ?? Colors.green)
          : Colors.transparent,
      ),
      child: isLoading
        ? _buildLoadingIndicator(theme)
        : _buildCheckboxContent(context, ref, theme, isCompleted),
    );
  }

  /// Loading indicator widget'ı
  Widget _buildLoadingIndicator(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(2),
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          theme.colorScheme.onPrimary,
        ),
      ),
    );
  }

  /// Checkbox içeriği
  Widget _buildCheckboxContent(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
    bool isCompleted,
  ) {
    return InkWell(
      onTap: () => _handleToggle(context, ref),
      borderRadius: BorderRadius.circular(4),
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: isCompleted
          ? Icon(
              Icons.check,
              size: (size ?? 24) * 0.7,
              color: Colors.white,
            )
          : null,
      ),
    );
  }

  /// Toggle işlemini handle et
  Future<void> _handleToggle(BuildContext context, WidgetRef ref) async {
    try {
      // Haptic feedback
      HapticFeedback.lightImpact();
      
      // Progress provider'dan toggle işlemi yap
      await ref.read(exerciseProgressProvider.notifier).toggleExerciseCompletion(
        programId,
        dayId,
        exerciseId,
        allExerciseIds,
      );

      // Başarı mesajını kontrol et ve göster
      final successMessage = ref.read(exerciseProgressSuccessProvider);
      if (successMessage != null && context.mounted) {
        _showSuccessMessage(context, successMessage);
        
        // Başarı mesajını temizle
        ref.read(exerciseProgressProvider.notifier).clearSuccessMessage();
      }
    } catch (e) {
      // Hata durumunda kullanıcıya bilgi ver
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Egzersiz tamamlama işlemi sırasında hata oluştu'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Başarı mesajını göster
  void _showSuccessMessage(BuildContext context, String message) {
    // Tüm egzersizler tamamlandı mesajı ise özel snackbar göster
    if (message.contains('Tüm egzersizleri tamamladınız')) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.fitness_center, color: Colors.white),
              SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'Tamam',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    } else {
      // Normal snackbar göster
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}

/// Responsive Exercise Completion Checkbox
/// Cihaz tipine göre boyut ayarlaması yapar
class ResponsiveExerciseCompletionCheckbox extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Gün ID'si
  final int dayId;
  
  /// Egzersiz ID'si
  final int exerciseId;
  
  /// Egzersiz adı
  final String exerciseName;
  
  /// Tüm egzersizlerin ID'leri
  final List<int> allExerciseIds;

  const ResponsiveExerciseCompletionCheckbox({
    super.key,
    required this.programId,
    required this.dayId,
    required this.exerciseId,
    required this.exerciseName,
    required this.allExerciseIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final size = constraints.maxWidth > 600 ? 28.0 : 20.0;

        return ExerciseCompletionCheckbox(
          programId: programId,
          dayId: dayId,
          exerciseId: exerciseId,
          exerciseName: exerciseName,
          allExerciseIds: allExerciseIds,
          size: size,
        );
      },
    );
  }
}

/// Exercise Row Widget
/// Egzersiz adı, set/tekrar bilgisi ve checkbox'ı içeren satır
class ExerciseRowWidget extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Gün ID'si
  final int dayId;
  
  /// Egzersiz ID'si
  final int exerciseId;
  
  /// Egzersiz adı
  final String exerciseName;
  
  /// Set sayısı
  final int sets;
  
  /// Tekrar sayısı (string olabilir: "12", "MAX", "12-15")
  final String reps;
  
  /// Egzersiz notları
  final String? notes;
  
  /// Tüm egzersizlerin ID'leri
  final List<int> allExerciseIds;

  const ExerciseRowWidget({
    super.key,
    required this.programId,
    required this.dayId,
    required this.exerciseId,
    required this.exerciseName,
    required this.sets,
    required this.reps,
    this.notes,
    required this.allExerciseIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final exerciseKey = '${programId}_${dayId}_$exerciseId';
    final isCompleted = ref.watch(exerciseCompletionProvider(exerciseKey));

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: isCompleted 
          ? Colors.green.withValues(alpha: 0.1)
          : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCompleted 
            ? Colors.green.withValues(alpha: 0.3)
            : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          ResponsiveExerciseCompletionCheckbox(
            programId: programId,
            dayId: dayId,
            exerciseId: exerciseId,
            exerciseName: exerciseName,
            allExerciseIds: allExerciseIds,
          ),
          
          const SizedBox(width: 12),
          
          // Egzersiz bilgileri
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Egzersiz adı
                Text(
                  exerciseName,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    decoration: isCompleted ? TextDecoration.lineThrough : null,
                    color: isCompleted 
                      ? theme.colorScheme.onSurface.withValues(alpha: 0.6)
                      : theme.colorScheme.onSurface,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // Set/Tekrar bilgisi
                Text(
                  '$sets set × $reps tekrar',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    decoration: isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
                
                // Notlar (varsa)
                if (notes != null && notes!.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    notes!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      fontStyle: FontStyle.italic,
                      decoration: isCompleted ? TextDecoration.lineThrough : null,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Tamamlandı ikonu (sadece tamamlanmışsa)
          if (isCompleted) ...[
            const SizedBox(width: 8),
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
          ],
        ],
      ),
    );
  }
}
