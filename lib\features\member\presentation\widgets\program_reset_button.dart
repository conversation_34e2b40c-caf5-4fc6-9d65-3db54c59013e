/// Program Reset Button Widget - GymKod Pro Mobile
///
/// Bu widget tüm antrenmanlar tamamlandığında program sıfırlama butonu sağlar.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/exercise_progress_provider.dart';
import '../../../../core/core.dart';

/// Program sıfırlama butonu widget'ı
class ProgramResetButton extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Program adı
  final String programName;
  
  /// Tüm antrenman günlerinin ID'leri
  final List<int> allWorkoutDayIds;
  
  /// Tüm egzersizlerin ID'leri (gün bazında)
  final Map<int, List<int>> allExerciseIds;

  const ProgramResetButton({
    super.key,
    required this.programId,
    required this.programName,
    required this.allWorkoutDayIds,
    required this.allExerciseIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Tüm antrenman günlerinin tamamlanıp tamamlanmadığını kontrol et
    bool allWorkoutsCompleted = true;
    for (final dayId in allWorkoutDayIds) {
      final sessionKey = '${programId}_$dayId';
      final isCompleted = ref.watch(workoutSessionCompletionProvider(sessionKey));
      if (!isCompleted) {
        allWorkoutsCompleted = false;
        break;
      }
    }

    // Eğer tüm antrenmanlar tamamlanmadıysa butonu gösterme
    if (!allWorkoutsCompleted) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ElevatedButton.icon(
        onPressed: () => _showResetConfirmationDialog(context, ref),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          elevation: 2,
        ),
        icon: const Icon(Icons.refresh, size: 20),
        label: const Text(
          'Programı Baştan Başlat',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// Sıfırlama onay dialog'u
  void _showResetConfirmationDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange, size: 28),
            const SizedBox(width: 10),
            const Text('Program Sıfırlama'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tebrikler! "$programName" programının tüm antrenmanlarını tamamladınız! 🎉',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Programı baştan başlatmak istiyor musunuz?',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        'Bu işlem:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text('• Tüm egzersiz tamamlama durumlarını sıfırlar'),
                  const Text('• Tüm antrenman seansı kayıtlarını siler'),
                  const Text('• Programı ilk günden başlatır'),
                  const SizedBox(height: 8),
                  const Text(
                    'Bu işlem geri alınamaz!',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _resetProgram(context, ref);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Programı Sıfırla'),
          ),
        ],
      ),
    );
  }

  /// Program sıfırlama işlemi
  Future<void> _resetProgram(BuildContext context, WidgetRef ref) async {
    try {
      // Haptic feedback
      HapticFeedback.mediumImpact();
      
      // Loading dialog göster
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Program sıfırlanıyor...'),
            ],
          ),
        ),
      );

      // Tüm günlerin egzersizlerini sıfırla
      for (final dayId in allWorkoutDayIds) {
        final exerciseIds = allExerciseIds[dayId] ?? [];
        if (exerciseIds.isNotEmpty) {
          await ref.read(exerciseProgressProvider.notifier).resetDayExercises(
            programId,
            dayId,
            exerciseIds,
          );
        }
      }

      // Loading dialog'u kapat
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Başarı mesajı göster
      if (context.mounted) {
        _showSuccessDialog(context);
      }

      LoggingService.info('Program reset completed', tag: 'PROGRAM_RESET');
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ProgramResetButton._resetProgram',
      );

      // Loading dialog'u kapat
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Hata mesajı göster
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Program sıfırlama işlemi sırasında hata oluştu'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Başarı dialog'u
  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 30),
            const SizedBox(width: 10),
            const Text('Başarılı! 🎉'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '"$programName" programı başarıyla sıfırlandı!',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.fitness_center, color: Colors.green),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Artık programınızı yeniden başlayabilirsiniz!',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Harika! 💪'),
          ),
        ],
      ),
    );
  }
}

/// Compact Program Reset Button
/// Daha küçük boyutlu sıfırlama butonu
class CompactProgramResetButton extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Program adı
  final String programName;
  
  /// Tüm antrenman günlerinin ID'leri
  final List<int> allWorkoutDayIds;
  
  /// Tüm egzersizlerin ID'leri (gün bazında)
  final Map<int, List<int>> allExerciseIds;

  const CompactProgramResetButton({
    super.key,
    required this.programId,
    required this.programName,
    required this.allWorkoutDayIds,
    required this.allExerciseIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Tüm antrenman günlerinin tamamlanıp tamamlanmadığını kontrol et
    bool allWorkoutsCompleted = true;
    for (final dayId in allWorkoutDayIds) {
      final sessionKey = '${programId}_$dayId';
      final isCompleted = ref.watch(workoutSessionCompletionProvider(sessionKey));
      if (!isCompleted) {
        allWorkoutsCompleted = false;
        break;
      }
    }

    // Eğer tüm antrenmanlar tamamlanmadıysa butonu gösterme
    if (!allWorkoutsCompleted) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: OutlinedButton.icon(
        onPressed: () => ProgramResetButton(
          programId: programId,
          programName: programName,
          allWorkoutDayIds: allWorkoutDayIds,
          allExerciseIds: allExerciseIds,
        )._showResetConfirmationDialog(context, ref),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.orange,
          side: const BorderSide(color: Colors.orange),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        icon: const Icon(Icons.refresh, size: 16),
        label: const Text(
          'Programı Baştan Başlat',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
