/// Day Completion Checkbox Widget - GymKod Pro Mobile
///
/// Bu widget antrenman günlerinin tamamlanma durumunu gösterir ve toggle işlemi yapar.
/// Dinlenme günlerinde görünmez, sadece antrenman günlerinde aktif.
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/workout_progress_provider.dart';

/// Gün tamamlama checkbox widget'ı
class DayCompletionCheckbox extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Gün ID'si
  final int dayId;
  
  /// Gün numarası
  final int dayNumber;
  
  /// Dinlenme günü mü?
  final bool isRestDay;
  
  /// Tüm antrenman günlerinin ID'leri (döngü kontrolü için)
  final List<int> allWorkoutDayIds;
  
  /// Checkbox boyutu
  final double? size;
  
  /// Özel renk
  final Color? activeColor;

  const DayCompletionCheckbox({
    super.key,
    required this.programId,
    required this.dayId,
    required this.dayNumber,
    required this.isRestDay,
    required this.allWorkoutDayIds,
    this.size,
    this.activeColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    // Dinlenme günlerinde checkbox gösterme
    if (isRestDay) {
      return const SizedBox.shrink();
    }

    // Loading durumunu kontrol et
    final dayKey = '${programId}_$dayId';
    final isLoading = ref.watch(dayCompletionLoadingProvider(dayKey));
    
    // Tamamlanma durumunu kontrol et
    final isCompleted = ref.watch(dayCompletionProvider(dayKey));

    return Container(
      width: size ?? 24,
      height: size ?? 24,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isCompleted 
            ? (activeColor ?? theme.colorScheme.primary)
            : theme.colorScheme.outline,
          width: 2,
        ),
        color: isCompleted 
          ? (activeColor ?? theme.colorScheme.primary)
          : Colors.transparent,
      ),
      child: isLoading
        ? _buildLoadingIndicator(theme)
        : _buildCheckboxContent(context, ref, theme, isCompleted),
    );
  }

  /// Loading indicator widget'ı
  Widget _buildLoadingIndicator(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(2),
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          theme.colorScheme.onPrimary,
        ),
      ),
    );
  }

  /// Checkbox içeriği
  Widget _buildCheckboxContent(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
    bool isCompleted,
  ) {
    return InkWell(
      onTap: () => _handleToggle(context, ref),
      borderRadius: BorderRadius.circular(4),
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: isCompleted
          ? Icon(
              Icons.check,
              size: (size ?? 24) * 0.7,
              color: theme.colorScheme.onPrimary,
            )
          : null,
      ),
    );
  }

  /// Toggle işlemini handle et
  Future<void> _handleToggle(BuildContext context, WidgetRef ref) async {
    try {
      // Haptic feedback
      HapticFeedback.lightImpact();
      
      // Progress provider'dan toggle işlemi yap
      await ref.read(workoutProgressProvider.notifier).toggleDayCompletion(
        programId,
        dayId,
        allWorkoutDayIds,
      );

      // Başarı mesajını kontrol et ve göster
      final successMessage = ref.read(workoutProgressSuccessProvider);
      if (successMessage != null && context.mounted) {
        _showSuccessMessage(context, successMessage);
        
        // Başarı mesajını temizle
        ref.read(workoutProgressProvider.notifier).clearSuccessMessage();
      }
    } catch (e) {
      // Hata durumunda kullanıcıya bilgi ver
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gün tamamlama işlemi sırasında hata oluştu'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Başarı mesajını göster
  void _showSuccessMessage(BuildContext context, String message) {
    // Döngü tamamlama mesajı ise özel dialog göster
    if (message.contains('döngü')) {
      _showCycleCompletionDialog(context, message);
    } else {
      // Normal snackbar göster
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Döngü tamamlama dialog'u
  void _showCycleCompletionDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.celebration, color: Colors.orange, size: 30),
            const SizedBox(width: 10),
            const Text('Tebrikler! 🎉'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 15),
            const LinearProgressIndicator(
              value: 1.0,
              backgroundColor: Colors.grey,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            const SizedBox(height: 10),
            Text(
              'Yeni döngüde başarılar! 💪',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.orange,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Devam Et! 🚀'),
          ),
        ],
      ),
    );
  }
}

/// Responsive Day Completion Checkbox
/// Cihaz tipine göre boyut ayarlaması yapar
class ResponsiveDayCompletionCheckbox extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Gün ID'si
  final int dayId;
  
  /// Gün numarası
  final int dayNumber;
  
  /// Dinlenme günü mü?
  final bool isRestDay;
  
  /// Tüm antrenman günlerinin ID'leri
  final List<int> allWorkoutDayIds;

  const ResponsiveDayCompletionCheckbox({
    super.key,
    required this.programId,
    required this.dayId,
    required this.dayNumber,
    required this.isRestDay,
    required this.allWorkoutDayIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final size = constraints.maxWidth > 600 ? 28.0 : 20.0;

        return DayCompletionCheckbox(
          programId: programId,
          dayId: dayId,
          dayNumber: dayNumber,
          isRestDay: isRestDay,
          allWorkoutDayIds: allWorkoutDayIds,
          size: size,
        );
      },
    );
  }
}
