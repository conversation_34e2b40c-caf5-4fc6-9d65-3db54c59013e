/// Program Reset Button Widget - GymKod Pro Mobile
///
/// Bu widget tüm antrenmanlar tamamlandığında program sıfırlama butonu sağlar.
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/exercise_progress_provider.dart';
import '../../../../core/core.dart';

/// Program sıfırlama butonu widget'ı
class ProgramResetButton extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Program adı
  final String programName;
  
  /// Tüm antrenman günlerinin ID'leri
  final List<int> allWorkoutDayIds;
  
  /// Tüm egzersizlerin ID'leri (gün bazında)
  final Map<int, List<int>> allExerciseIds;

  const ProgramResetButton({
    super.key,
    required this.programId,
    required this.programName,
    required this.allWorkoutDayIds,
    required this.allExerciseIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Tüm antrenman günlerinin tamamlanıp tamamlanmadığını kontrol et
    bool allWorkoutsCompleted = true;
    for (final dayId in allWorkoutDayIds) {
      final sessionKey = '${programId}_$dayId';
      final isCompleted = ref.watch(workoutSessionCompletionProvider(sessionKey));
      if (!isCompleted) {
        allWorkoutsCompleted = false;
        break;
      }
    }

    // Eğer tüm antrenmanlar tamamlanmadıysa butonu gösterme
    if (!allWorkoutsCompleted) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ElevatedButton.icon(
        onPressed: () => _showResetConfirmationDialog(context, ref),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          elevation: 2,
        ),
        icon: const Icon(Icons.refresh, size: 20),
        label: const Text(
          'Programı Baştan Başlat',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// Sıfırlama onay dialog'u
  void _showResetConfirmationDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          constraints: BoxConstraints(
            maxWidth: 400,
            minHeight: 200,
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  Icon(Icons.refresh, color: Colors.orange, size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Program Yeniden Başlat',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Content
              Text(
                'Tebrikler! "$programName" programının tüm antrenmanlarını tamamladınız! 🎉',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                  fontSize: 18,
                  height: 1.4,
                ),
              ),

              const SizedBox(height: 20),

              Text(
                'Antrenmanı yeniden başlatmak istiyor musunuz?',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  height: 1.3,
                  color: Colors.grey[700],
                ),
              ),

              const SizedBox(height: 32),

              // Buttons
              Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.red, width: 2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: const Text(
                          'İptal',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  Expanded(
                    child: SizedBox(
                      height: 48,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _resetProgram(context, ref);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: const Text(
                          'Yeniden Başlat',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Program sıfırlama işlemi
  Future<void> _resetProgram(BuildContext context, WidgetRef ref) async {
    try {
      // Haptic feedback
      HapticFeedback.mediumImpact();
      
      // Loading dialog göster
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Program sıfırlanıyor...'),
            ],
          ),
        ),
      );

      // Tüm günlerin egzersizlerini sıfırla
      for (final dayId in allWorkoutDayIds) {
        final exerciseIds = allExerciseIds[dayId] ?? [];
        if (exerciseIds.isNotEmpty) {
          await ref.read(exerciseProgressProvider.notifier).resetDayExercises(
            programId,
            dayId,
            exerciseIds,
          );
        }
      }

      // Loading dialog'u kapat
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Başarı mesajı göster
      if (context.mounted) {
        _showSuccessDialog(context);
      }

      LoggingService.info('Program reset completed', tag: 'PROGRAM_RESET');
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ProgramResetButton._resetProgram',
      );

      // Loading dialog'u kapat
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Hata mesajı göster
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Program sıfırlama işlemi sırasında hata oluştu'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Başarı dialog'u
  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          constraints: BoxConstraints(
            maxWidth: 400,
            minHeight: 200,
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Başarılı! 🎉',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Content
              Text(
                '"$programName" programı başarıyla sıfırlandı!',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  height: 1.4,
                ),
              ),

              const SizedBox(height: 20),

              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(Icons.fitness_center, color: Colors.green, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Artık programınızı yeniden başlayabilirsiniz!',
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Button
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    'Harika! 💪',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
